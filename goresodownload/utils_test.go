package goresodownload

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestGetPropTsForPath(t *testing.T) {
	// 测试时间
	testTime1 := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	testTime2 := time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC)
	testTime3 := time.Date(2023, 3, 1, 12, 0, 0, 0, time.UTC)
	testTime4 := time.Date(2023, 4, 1, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name     string
		record   bson.M
		board    string
		expected time.Time
		wantErr  bool
	}{
		{
			name: "TRB board - ListingContractDate has highest priority",
			record: bson.M{
				"ListingContractDate":    testTime1,
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:    "TRB",
			expected: testTime1,
			wantErr:  false,
		},
		{
			name: "TRB board - ModificationTimestamp when ListingContractDate missing",
			record: bson.M{
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:    "TRB",
			expected: testTime2,
			wantErr:  false,
		},
		{
			name: "TRB board - OriginalEntryTimestamp when higher priority fields missing",
			record: bson.M{
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:    "TRB",
			expected: testTime3,
			wantErr:  false,
		},
		{
			name: "TRB board - ts as fallback",
			record: bson.M{
				"ts": testTime4,
			},
			board:    "TRB",
			expected: testTime4,
			wantErr:  false,
		},
		{
			name: "Non-TRB board - ListingContractDate has priority",
			record: bson.M{
				"ListingContractDate": testTime1,
				"ts":                  testTime4,
			},
			board:    "CAR",
			expected: testTime1,
			wantErr:  false,
		},
		{
			name: "Non-TRB board - ts when ListingContractDate missing",
			record: bson.M{
				"ts": testTime4,
			},
			board:    "CAR",
			expected: testTime4,
			wantErr:  false,
		},
		{
			name: "TRB board - primitive.DateTime type",
			record: bson.M{
				"ListingContractDate": primitive.NewDateTimeFromTime(testTime1),
			},
			board:   "TRB",
			wantErr: false,
			// Note: primitive.DateTime may return local time, so we check separately
		},
		{
			name: "TRB board - string type timestamp",
			record: bson.M{
				"ListingContractDate": "2023-01-01T12:00:00Z",
			},
			board:    "TRB",
			expected: testTime1,
			wantErr:  false,
		},
		{
			name: "TRB board - no valid timestamp",
			record: bson.M{
				"someOtherField": "value",
			},
			board:   "TRB",
			wantErr: true,
		},
		{
			name: "Non-TRB board - no valid timestamp",
			record: bson.M{
				"someOtherField": "value",
			},
			board:   "CAR",
			wantErr: true,
		},
		{
			name: "TRB board - nil values should be skipped",
			record: bson.M{
				"ListingContractDate":    nil,
				"ModificationTimestamp":  nil,
				"OriginalEntryTimestamp": testTime3,
			},
			board:    "TRB",
			expected: testTime3,
			wantErr:  false,
		},
		{
			name: "TRB board - zero time should be skipped",
			record: bson.M{
				"ListingContractDate": time.Time{},
				"ts":                  testTime4,
			},
			board:    "TRB",
			expected: testTime4,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetPropTsForPath(tt.record, tt.board)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.expected.IsZero() {
					// For primitive.DateTime test case, just check it's not zero
					assert.False(t, result.IsZero())
				} else {
					assert.Equal(t, tt.expected, result)
				}
			}
		})
	}
}

func TestParseTimestamp(t *testing.T) {
	testTime := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name     string
		value    interface{}
		expected time.Time
		wantErr  bool
	}{
		{
			name:     "time.Time type",
			value:    testTime,
			expected: testTime,
			wantErr:  false,
		},
		{
			name:    "primitive.DateTime type",
			value:   primitive.NewDateTimeFromTime(testTime),
			wantErr: false,
			// Note: primitive.DateTime may return local time, check separately
		},
		{
			name:     "string RFC3339 format",
			value:    "2023-01-01T12:00:00Z",
			expected: testTime,
			wantErr:  false,
		},
		{
			name:     "string date only format",
			value:    "2023-01-01",
			expected: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			wantErr:  false,
		},
		{
			name:     "string with milliseconds",
			value:    "2023-01-01T12:00:00.000Z",
			expected: testTime,
			wantErr:  false,
		},
		{
			name:    "string with timezone",
			value:   "2023-01-01T12:00:00-07:00",
			wantErr: false,
			// Note: timezone parsing may vary, check separately
		},
		{
			name:    "invalid string format",
			value:   "invalid-date",
			wantErr: true,
		},
		{
			name:    "unsupported type - int",
			value:   123456789,
			wantErr: true,
		},
		{
			name:    "unsupported type - nil",
			value:   nil,
			wantErr: true,
		},
		{
			name:    "unsupported type - bool",
			value:   true,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parseTimestamp(tt.value)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.expected.IsZero() {
					// For special cases like primitive.DateTime and timezone, just check it's not zero
					assert.False(t, result.IsZero())
				} else {
					assert.Equal(t, tt.expected, result)
				}
			}
		})
	}
}

func TestShouldSkipPhoPGeneration(t *testing.T) {
	tests := []struct {
		name     string
		record   bson.M
		expected bool
	}{
		{
			name: "phoP exists with valid path",
			record: bson.M{
				"phoP": "1234/abc12",
			},
			expected: true,
		},
		{
			name: "phoP exists but empty string",
			record: bson.M{
				"phoP": "",
			},
			expected: false,
		},
		{
			name: "phoP exists but nil",
			record: bson.M{
				"phoP": nil,
			},
			expected: false,
		},
		{
			name: "phoP does not exist",
			record: bson.M{
				"otherField": "value",
			},
			expected: false,
		},
		{
			name:     "nil record",
			record:   nil,
			expected: false,
		},
		{
			name:     "empty record",
			record:   bson.M{},
			expected: false,
		},
		{
			name: "phoP with whitespace only",
			record: bson.M{
				"phoP": "   ",
			},
			expected: true, // 非空字符串被认为是有效的
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := shouldSkipPhoPGeneration(tt.record)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestPhoPLogicIntegration 测试phoP逻辑的集成测试
func TestPhoPLogicIntegration(t *testing.T) {
	testTime1 := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	testTime2 := time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC)
	testTime3 := time.Date(2023, 3, 1, 12, 0, 0, 0, time.UTC)
	testTime4 := time.Date(2023, 4, 1, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name          string
		record        bson.M
		board         string
		expectedSkip  bool
		expectedTsErr bool
		description   string
	}{
		{
			name: "TRB board with existing phoP - should skip",
			record: bson.M{
				"_id":                    "test1",
				"phoP":                   "/1234/abc12",
				"ListingContractDate":    testTime1,
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:        "TRB",
			expectedSkip: true,
			description:  "当记录已有phoP字段时，应该跳过生成",
		},
		{
			name: "TRB board without phoP - should generate with ListingContractDate priority",
			record: bson.M{
				"_id":                    "test2",
				"ListingContractDate":    testTime1,
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "TRB board应该使用ListingContractDate作为最高优先级",
		},
		{
			name: "TRB board with ModificationTimestamp priority",
			record: bson.M{
				"_id":                    "test3",
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "当ListingContractDate不存在时，应该使用ModificationTimestamp",
		},
		{
			name: "TRB board with OriginalEntryTimestamp priority",
			record: bson.M{
				"_id":                    "test4",
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "当更高优先级字段不存在时，应该使用OriginalEntryTimestamp",
		},
		{
			name: "TRB board with ts fallback",
			record: bson.M{
				"_id": "test5",
				"ts":  testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "当其他时间戳字段都不存在时，应该使用ts作为fallback",
		},
		{
			name: "CAR board with ListingContractDate priority",
			record: bson.M{
				"_id":                 "test6",
				"ListingContractDate": testTime1,
				"ts":                  testTime4,
			},
			board:        "CAR",
			expectedSkip: false,
			description:  "非TRB board应该优先使用ListingContractDate",
		},
		{
			name: "CAR board with ts fallback",
			record: bson.M{
				"_id": "test7",
				"ts":  testTime4,
			},
			board:        "CAR",
			expectedSkip: false,
			description:  "非TRB board当ListingContractDate不存在时，应该使用ts",
		},
		{
			name: "TRB board with empty phoP - should generate",
			record: bson.M{
				"_id":                 "test8",
				"phoP":                "",
				"ListingContractDate": testTime1,
				"ts":                  testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "当phoP字段为空字符串时，应该重新生成",
		},
		{
			name: "TRB board with nil phoP - should generate",
			record: bson.M{
				"_id":                 "test9",
				"phoP":                nil,
				"ListingContractDate": testTime1,
				"ts":                  testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "当phoP字段为nil时，应该重新生成",
		},
		{
			name: "TRB board with no valid timestamps - should error",
			record: bson.M{
				"_id":        "test10",
				"otherField": "value",
			},
			board:         "TRB",
			expectedSkip:  false,
			expectedTsErr: true,
			description:   "当没有有效的时间戳字段时，应该返回错误",
		},
		{
			name: "CAR board with no valid timestamps - should error",
			record: bson.M{
				"_id":        "test11",
				"otherField": "value",
			},
			board:         "CAR",
			expectedSkip:  false,
			expectedTsErr: true,
			description:   "非TRB board当没有有效时间戳字段时，应该返回错误",
		},
		{
			name: "TRB board with primitive.DateTime types",
			record: bson.M{
				"_id":                    "test12",
				"ListingContractDate":    primitive.NewDateTimeFromTime(testTime1),
				"ModificationTimestamp":  primitive.NewDateTimeFromTime(testTime2),
				"OriginalEntryTimestamp": primitive.NewDateTimeFromTime(testTime3),
				"ts":                     primitive.NewDateTimeFromTime(testTime4),
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "应该正确处理primitive.DateTime类型的时间戳",
		},
		{
			name: "TRB board with string timestamp",
			record: bson.M{
				"_id":                 "test13",
				"ListingContractDate": "2023-01-01T12:00:00Z",
				"ts":                  testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "应该正确解析字符串格式的时间戳",
		},
		{
			name: "TRB board with nil values should skip to next priority",
			record: bson.M{
				"_id":                    "test14",
				"ListingContractDate":    nil,
				"ModificationTimestamp":  nil,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "应该跳过nil值，使用下一个优先级的时间戳",
		},
		{
			name: "TRB board with zero time should skip to next priority",
			record: bson.M{
				"_id":                 "test15",
				"ListingContractDate": time.Time{},
				"ts":                  testTime4,
			},
			board:        "TRB",
			expectedSkip: false,
			description:  "应该跳过零值时间，使用下一个优先级的时间戳",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Logf("测试描述: %s", tt.description)

			// 测试 shouldSkipPhoPGeneration
			shouldSkip := shouldSkipPhoPGeneration(tt.record)
			assert.Equal(t, tt.expectedSkip, shouldSkip, "shouldSkipPhoPGeneration 结果不符合预期")

			if !shouldSkip {
				// 测试 GetPropTsForPath
				ts, err := GetPropTsForPath(tt.record, tt.board)

				if tt.expectedTsErr {
					assert.Error(t, err, "应该返回时间戳获取错误")
				} else {
					assert.NoError(t, err, "不应该返回时间戳获取错误")
					assert.False(t, ts.IsZero(), "返回的时间戳不应该为零值")

					// 验证时间戳优先级逻辑
					if tt.board == "TRB" {
						// TRB board 优先级验证
						if tt.record["ListingContractDate"] != nil {
							expectedTs, err := parseTimestamp(tt.record["ListingContractDate"])
							if err == nil && !expectedTs.IsZero() {
								assert.Equal(t, expectedTs, ts, "应该使用ListingContractDate")
							}
						} else if tt.record["ModificationTimestamp"] != nil {
							expectedTs, err := parseTimestamp(tt.record["ModificationTimestamp"])
							if err == nil && !expectedTs.IsZero() {
								assert.Equal(t, expectedTs, ts, "应该使用ModificationTimestamp")
							}
						} else if tt.record["OriginalEntryTimestamp"] != nil {
							expectedTs, err := parseTimestamp(tt.record["OriginalEntryTimestamp"])
							if err == nil && !expectedTs.IsZero() {
								assert.Equal(t, expectedTs, ts, "应该使用OriginalEntryTimestamp")
							}
						} else if tt.record["ts"] != nil {
							expectedTs, err := parseTimestamp(tt.record["ts"])
							if err == nil && !expectedTs.IsZero() {
								assert.Equal(t, expectedTs, ts, "应该使用ts作为fallback")
							}
						}
					} else {
						// 非TRB board 优先级验证
						if tt.record["ListingContractDate"] != nil {
							expectedTs, err := parseTimestamp(tt.record["ListingContractDate"])
							if err == nil && !expectedTs.IsZero() {
								assert.Equal(t, expectedTs, ts, "应该使用ListingContractDate")
							}
						} else if tt.record["ts"] != nil {
							expectedTs, err := parseTimestamp(tt.record["ts"])
							if err == nil && !expectedTs.IsZero() {
								assert.Equal(t, expectedTs, ts, "应该使用ts")
							}
						}
					}
				}
			}
		})
	}
}

// TestPhoPLogicEdgeCases 测试边界情况
func TestPhoPLogicEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		record      bson.M
		board       string
		expectError bool
		description string
	}{
		{
			name:        "nil record",
			record:      nil,
			board:       "TRB",
			expectError: true,
			description: "nil记录应该返回错误",
		},
		{
			name:        "empty record",
			record:      bson.M{},
			board:       "TRB",
			expectError: true,
			description: "空记录应该返回错误",
		},
		{
			name: "invalid timestamp type",
			record: bson.M{
				"_id":                 "test1",
				"ListingContractDate": 123456, // 无效类型
			},
			board:       "TRB",
			expectError: true,
			description: "无效的时间戳类型应该返回错误",
		},
		{
			name: "mixed valid and invalid timestamps",
			record: bson.M{
				"_id":                   "test2",
				"ListingContractDate":   123456,                                       // 无效类型
				"ModificationTimestamp": time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC), // 有效类型
			},
			board:       "TRB",
			expectError: false,
			description: "应该跳过无效时间戳，使用有效的时间戳",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Logf("测试描述: %s", tt.description)

			_, err := GetPropTsForPath(tt.record, tt.board)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNormalizePhoP(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "remove leading slash",
			input:    "/1234/abc12",
			expected: "1234/abc12",
		},
		{
			name:     "no leading slash",
			input:    "1234/abc12",
			expected: "1234/abc12",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "only slash",
			input:    "/",
			expected: "",
		},
		{
			name:     "multiple leading slashes",
			input:    "//1234/abc12",
			expected: "/1234/abc12", // 只移除第一个
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizePhoP(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

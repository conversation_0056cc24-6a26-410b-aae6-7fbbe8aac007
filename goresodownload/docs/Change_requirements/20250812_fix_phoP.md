# 需求 [fix_phoP]

## 反馈

1. 现在phoP字段为"phoP" : "/1273/b3a04"， 需要去除第一个"/"

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-08-11

## 原因
1. 没有必要多保存一个"/"

## 解决办法
1. phoP 保存前check，如果有第一个"/"，就去除
2. 增加batch，将数据库中已经有的phoP的第一个"/" 去除


## 是否需要补充UT

1. 不需要

## 确认日期:    2025-08-12

## online-step
1. 重启goresodownload
2. 运行batch
```
./start.sh  -n fixPhoP -d "goresodownload" -cmd "cmd/batch/fixPhoP/main.go -dryrun -board=TRB"
./start.sh  -n fixPhoP -d "goresodownload" -cmd "cmd/batch/fixPhoP/main.go -dryrun -board=BRE"
./start.sh  -n fixPhoP -d "goresodownload" -cmd "cmd/batch/fixPhoP/main.go -dryrun -board=DDF"
```

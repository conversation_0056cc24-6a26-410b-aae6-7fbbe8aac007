package goresodownload

import (
	"fmt"
	"strings"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetPropTsForPath 根据board类型和记录内容获取用于路径计算的时间戳
// TRB board优先级: ListingContractDate > ModificationTimestamp > OriginalEntryTimestamp > ts
// 非TRB board优先级: ListingContractDate > ts
func GetPropTsForPath(record bson.M, board string) (time.Time, error) {
	var fieldNames []string

	switch board {
	case "TRB":
		fieldNames = []string{"ListingContractDate", "ModificationTimestamp", "OriginalEntryTimestamp", "ts"}
	case "BRE":
		fieldNames = []string{"ListingContractDate", "BridgeModificationTimestamp", "OriginalEntryTimestamp", "ts"}
	case "DDF":
		fieldNames = []string{"OriginalEntryTimestamp", "ModificationTimestamp", "ts"}
	default: // CAR, EDM, CLG, default
		fieldNames = []string{"ListingContractDate", "ts"}
	}

	// 按优先级顺序尝试获取时间戳
	for _, fieldName := range fieldNames {
		if value, exists := record[fieldName]; exists && value != nil {
			ts, err := parseTimestamp(value)
			if err == nil && !ts.IsZero() {
				return ts, nil
			}
		}
	}

	return time.Time{}, fmt.Errorf("no valid timestamp found in record for board %s", board)
}

// parseTimestamp 解析各种类型的时间戳字段
func parseTimestamp(value interface{}) (time.Time, error) {
	switch v := value.(type) {
	case time.Time:
		return v, nil
	case primitive.DateTime:
		return v.Time(), nil
	case string:
		// 尝试解析常见的时间格式
		formats := []string{
			"2006-01-02",
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.000Z",
			"2006-01-02T15:04:05-07:00",
			"2006-01-02T15:04:05.000-07:00",
		}
		for _, format := range formats {
			if t, err := time.Parse(format, v); err == nil {
				return t, nil
			}
		}
		return time.Time{}, fmt.Errorf("unable to parse time string: %s", v)
	default:
		return time.Time{}, fmt.Errorf("unsupported timestamp type: %T", v)
	}
}

// shouldSkipPhoPGeneration 检查记录是否已经有phoP字段，如果有则跳过phoP生成
func shouldSkipPhoPGeneration(record bson.M) bool {
	if record == nil {
		return false
	}

	phoP, exists := record["phoP"]
	if !exists {
		return false
	}

	// 检查phoP字段是否为空或nil
	if phoP == nil || phoP == "" {
		return false
	}

	return true
}

func GetPhoP(doc bson.M, id string, board string) (string, error) {
	if phoP, ok := doc["phoP"].(string); ok && phoP != "" {
		return NormalizePhoP(phoP), nil
	}
	// 使用新的PropTs获取逻辑，保证与文件路径计算的一致性
	ts, err := GetPropTsForPath(doc, board)
	if err != nil {
		golog.Error("Failed to get PropTs for path",
			"_id", id, "board", board, "error", err)
		return "", fmt.Errorf("failed to get PropTs for path: %w", err)
	}
	var filePath string
	filePath, err = levelStore.GetFullFilePathForProp(ts, board, id)
	if err != nil {
		return "", err
	}
	return NormalizePhoP(filePath), nil
}

// NormalizePhoP ensures phoP path does not start with a leading '/'
// Example: "/1273/b3a04" -> "1273/b3a04"
func NormalizePhoP(path string) string {
	if path == "" {
		return path
	}
	if strings.HasPrefix(path, "/") {
		// Only remove the first leading slash per requirement
		return strings.TrimPrefix(path, "/")
	}
	return path
}

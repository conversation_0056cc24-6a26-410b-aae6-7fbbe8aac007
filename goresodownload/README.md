# GoResoDownload

GoResoDownload is a media file download and management system for real estate listings. It handles media file downloads, updates, and deletions for different board types.

## Features

- Hierarchical directory structure (L1/L2) based on dates and board types
- Automatic UUID5 generation for L2 directories
- MongoDB integration for directory mapping storage
- Support for multiple board types (TRB, DDF, BRE, CLG, OTW, EDM, CAR, USER)
- Directory statistics tracking
- Temporary directory mapping with automatic cleanup
- Media file download and management for multiple board types (CAR, DDF, BRE, EDM, TRB)
- Automatic thumbnail generation
- Concurrent download and deletion processing
- MongoDB change stream monitoring
- Directory structure management (L1/L2)
- Failed task tracking and retry mechanism
- Process monitoring and status tracking
- **Resource download queue system** with priority-based processing
- **Priority calculator** for intelligent task scheduling based on property characteristics



## Directory Structure

The system uses a two-level directory structure:
- L1: Year-week based folders (e.g., 750, 751, etc.)
- L2: UUID5-based subfolders (e.g., "abc12", "def34", etc.)

### L1 Calculation
- Each year has 50 folders (weeks)
- Weeks 0 and 49+ are merged into one folder
- 2015 starts at 750
- 2024 maps to 1200
- 2025 maps to 1250

### Board Types and L2 Sizes
- TRB: 512 L2 directories
- DDF: 1024 L2 directories
- BRE: 64 L2 directories
- CLG: 64 L2 directories
- OTW: 64 L2 directories
- EDM: 64 L2 directories
- CAR: 256 L2 directories
- USER: 512 L2 directories

## Usage

### Basic Usage

```shell
cd goresodownload
go mod tidy


cd rmconfig
# dryrun
./start.sh -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force -dryrun"
# run board: CAR|DDF|BRE|EDM|TRB
./start.sh -t batch -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force"


```

### Resource Download Queue

The system implements a priority-based download queue to efficiently process media downloads:

- **Queue Management**: Items are added to the queue when media changes are detected
- **Priority Calculation**: Each queue item is assigned a priority score based on property characteristics
- **Batch Processing**: Items are processed in batches with configurable batch sizes
- **Concurrent Processing**: Multiple items can be processed simultaneously using goroutines
- **Automatic Cleanup**: Successfully processed items are automatically removed from the queue
- **Locking Mechanism**: Items are locked during processing to prevent duplicate work

### Priority Calculator

The priority calculator assigns scores to download tasks based on multiple factors:

#### Priority Factors
- **No Photos Bonus** (30,000 points): Properties without photos get highest priority
- **Active Status** (10,000 points): Active listings are prioritized
- **Current Year** (5,000 points): Recent listings get bonus points
- **GTA Region** (500 points): Greater Toronto Area properties get regional bonus
- **New Listing** (300 points): Listings within 3 days get new listing bonus
- **Residential Property** (200 points): Residential/condo properties get type bonus
- **Status Bonuses**: Sold (50 points), Leased (30 points)
- **Ontario Province** (20 points): ON properties get province bonus
- **Days on Market** (30-DOM points): Lower DOM gets higher priority

#### Board-Specific Normalization
- **TRB**: Uses `ListingContractDate`, `CountyOrParish`, `PropertyType`, `StateOrProvince`, `DaysOnMarket`
- **BRE**: Uses `ListingContractDate`, `CountyOrParish`, `PropertyType`, `StateOrProvince`
- **DDF**: Uses `OriginalEntryTimestamp`, `CityRegion`, `PropertySubType`, `StateOrProvince`
- **EDM**: Uses `ListingContractDate`, `StateRegion`, `PropertyType`, `StateOrProvince`, `CumulativeDaysOnMarket`
- **CAR**: Uses `ListingContractDate`, `CountyOrParish`, `PropertyType`, `StateOrProvince`, `DaysOnMarket`

## Online Deployment Steps

### Step 1: Configuration Setup

#### 1.1 Local Configuration (local.ini)
Create or update your `local.ini` configuration file:

```ini
[imageStore]
reso_treb_image_dir = ["/mnt/ca6m0/imgs/MLS/TRB","/mnt/ca7m0/imgs/MLS/TRB"]
reso_car_image_dir = ["/mnt/ca6m0/imgs/MLS/CAR","/mnt/ca7m0/imgs/MLS/CAR"]
reso_crea_image_dir = ["/mnt/ca6m0/imgs/MLS/DDF","/mnt/ca7m0/imgs/MLS/DDF"]
reso_bcre_image_dir = ["/mnt/ca6m0/imgs/MLS/BRE","/mnt/ca7m0/imgs/MLS/BRE"]
reso_edm_image_dir = ["/mnt/ca6m0/imgs/MLS/EDM","/mnt/ca7m0/imgs/MLS/EDM"]

[golog]
dir = "/YOUR_PATH/go/logs"
format = "txt"
level = "info"
standard_output = true
#error = "error.log"
#info = "info.log"
#verbose = "verbose.log"   # comment these since we use standard_output

[serverBase]
srcGoBasePath = "/YOUR_PATH/go"
```

### Step 2: Application Deployment

#### 2.1 Build and Deploy
```bash
cd goresodownload
go mod tidy
```

#### 2.2 Start Services Using start.sh
```bash
cd rmconfig
make build    # Build bin/goresodownload.bin

# Dry run mode (for testing)
./start.sh -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force -dryrun"

# Production mode for different boards
./start.sh -t batch -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force"
./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
./start.sh -t batch -n goresodownloadDDF -d "goresodownload" -cmd "bin/goresodownload.bin -board DDF -force"
./start.sh -t batch -n goresodownloadBRE -d "goresodownload" -cmd "bin/goresodownload.bin -board BRE -force"
./start.sh -t batch -n goresodownloadEDM -d "goresodownload" -cmd "bin/goresodownload.bin -board EDM -force"

# With custom batch size [default batchSize is 2]
./start.sh -t batch -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force -batchSize 3"
```



## Configuration Reference

The package requires the following configuration:

1. **MongoDB connection** for:
   - Watch collections (board-specific)
   - Merged collections
   - Failed tasks collection
   - Process status collection
   - System data collection

2. **Storage paths** for each board type

3. **Downloader configuration**:
   - Download concurrency(default: 5)
   - Delete concurrency(default: 5)
   - Max retries
   - Consecutive failures threshold

4. **Queue configuration**:
   - Batch size for processing (default: 4 items)
   - Lock timeout for queue items (default: 30 seconds)
   - Priority calculation parameters

## Testing

Run end-to-end tests with:

```bash
go test -v ./test/e2e
```

Test coverage can be checked with:

```bash
go test -cover ./...
```

### Priority Calculator Tests

Run priority calculator tests specifically:

```bash
go test -v ./priority_calculator_test.go
```

The priority calculator tests cover:
- Different board types (TRB, CAR, DDF, BRE, EDM)
- Various property statuses and characteristics
- Edge cases and normalization scenarios
- Priority score calculations and tolerances

## Dependencies

- github.com/real-rm/gobase
- github.com/real-rm/gofile
- github.com/real-rm/golevelstore
- github.com/real-rm/golog
- github.com/real-rm/gomongo
- github.com/real-rm/goprocess
- github.com/real-rm/gowatch
- go.mongodb.org/mongo-driver/mongo

## Architecture Components

### Core Components
- **MediaDiffAnalyzer**: Analyzes changes in property media and determines download/delete tasks
- **Downloader**: Handles actual file downloads and deletions with retry mechanisms
- **ResourceDownloadQueue**: Manages priority-based queue for download tasks
- **PriorityCalculator**: Calculates priority scores for queue items based on property characteristics
- **DirKeyStore**: Manages hierarchical directory structure and statistics
- **ProcessMonitor**: Tracks process status and health monitoring


## Data Flow

### 1. Change Detection
- MongoDB change streams monitor property collections
- Media field changes trigger queue item creation
- Priority is calculated based on property characteristics

### 2. Queue Processing
- Items are added to the download queue with calculated priorities
- Queue processor runs continuously, processing items in batches
- Concurrent processing with goroutines for efficiency

### 3. Media Analysis & Download
- MediaDiffAnalyzer determines required downloads and deletions
- Downloader executes actual file operations
- Failed tasks are tracked for retry

## Merged saved
1. DB:
phoLH:[int32,int32]
tnLH: int32
docHL:["int32.pdf","int32.mp3"] 


2. pic dir: L1/L2/sid_base62.jpg
1274/ffc41/C11910492_B8P8Uz.jpg


```go
step1: get L1/L2 using ts,board,sid
// need import package
import levelStore "github.com/real-rm/golevelstore"

path, err := levelStore.GetFullFilePathForProp(propTs, "TRB", "********")
if err != nil {
    // Handle error
}
fmt.Println(path) // Output: /1200/abc12

step2: get hash int32
// keyStr is the media key, for treb: key, other board: MediaKey.
hash := levelStore.MurmurToInt32(keyStr) // Output(int32): 2004

step3: get base62
base62, err := levelStore.Int32ToBase62(hash)
```

## 根据merged信息，获取图片链接
1. 获取本地路径，rmconfig 中获取
[imageStore]
reso_bcre_image_dir = [ ]
reso_car_image_dir = [ ]
reso_crea_image_dir = [ ]
reso_edm_image_dir = [ ]
reso_treb_image_dir = [ ]

2. 根据prop的 ts,board,sid信息，计算L1，L2
3. 根据prop的phoLH，tnLH，拼接出图片路径 
L1/L2/sid_base62.jpg


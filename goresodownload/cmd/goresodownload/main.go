/*
This is goresodownload batch.
New config:
	./start.sh -n goresodownload -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force --batchSize 30 -propConcurrency 20 -dryrun"
createDate:    2025-06-05
Author:        Maggie
Run frequency: always
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os/signal"
	"strings"
	"sync"
	"time"

	gobase "github.com/real-rm/gobase"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goprocess"
	"github.com/real-rm/goresodownload"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	"github.com/real-rm/gowatch"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	gWatchedObject      *gowatch.WatchObject
	gUpdateSysData      gowatch.UpdateSysDataFunc
	SysdataCol          *gomongo.MongoCollection
	FailedCol           *gomongo.MongoCollection
	QueueCol            *gomongo.MongoCollection
	QueueColCa6         *gomongo.MongoCollection // ca6队列集合（用于watch模式双写）
	QueueColCa7         *gomongo.MongoCollection // ca7队列集合（用于watch模式双写）
	gProcessMonitor     *goprocess.ProcessMonitor
	dryRun              bool
	force               bool
	gBoardType          string
	diskType            string // 新增：磁盘类型参数 (ca6/ca7)
	isWatch             bool   // 新增：是否启用监听功能
	analyzer            *goresodownload.MediaDiffAnalyzer
	downloader          *goresodownload.Downloader
	dirStore            *levelStore.DirKeyStore
	downloadQueue       *goresodownload.ResourceDownloadQueue
	downloadQueueCa6    *goresodownload.ResourceDownloadQueue   // ca6队列（用于watch模式双写）
	downloadQueueCa7    *goresodownload.ResourceDownloadQueue   // ca7队列（用于watch模式双写）
	inconsistentManager *goresodownload.InconsistentDataManager // 不一致数据管理器
	lastTokenPrintTs    = time.Now()
	gStat               = make(map[string]int)
	tokenMu             sync.Mutex
	statMu              sync.Mutex
	speedMeter          *gospeedmeter.SpeedMeter
	batchSize           int
	expireHours         int // 定时重置speedMeter的小时数
	propConcurrency     int
)

// Application constants
const (
	HALF_DAY                      = 12 * time.Hour
	UPDATE_PROCESS_STATE_INTERVAL = 10 * time.Minute // 10 minutes

	// Default configuration values
	DEFAULT_BATCH_USER_ID = "batch" // Default user ID for batch operations
	DEFAULT_BATCH_SIZE    = 4       // Default batch size for processing

	// ProcessMonitor configuration
	ProcessMonitorMultiplier = 1.5 // Allow 50% more time than the update interval
)

var PROCESS_STATUS_ID string

func init() {
	// CRITICAL: Replace default HTTP transport immediately to prevent connection leaks
	http.DefaultTransport = &http.Transport{
		DisableKeepAlives:     true,
		MaxIdleConns:          0,
		MaxIdleConnsPerHost:   0,
		MaxConnsPerHost:       1,
		IdleConnTimeout:       1 * time.Second,
		ResponseHeaderTimeout: 10 * time.Second,
		ExpectContinueTimeout: 3 * time.Second,
		TLSHandshakeTimeout:   8 * time.Second,
		DisableCompression:    true,
		ForceAttemptHTTP2:     false,
		DialContext: (&net.Dialer{
			Timeout:   8 * time.Second,
			KeepAlive: 0, // Completely disable keep-alive
		}).DialContext,
	}

	// Parse command line flags
	flag.StringVar(&gBoardType, "board", "", "Board type (CAR/DDF/BRE/EDM/TRB)")
	flag.StringVar(&diskType, "disk", "", "Disk type for queue selection (ca6/ca7)")
	flag.BoolVar(&isWatch, "isWatch", false, "Enable watch functionality for RNI property table")
	flag.BoolVar(&dryRun, "dryrun", false, "Run in dry run mode")
	flag.BoolVar(&force, "force", false, "Force start a new process")
	flag.IntVar(&batchSize, "batchSize", DEFAULT_BATCH_SIZE, "Batch size for processing queue items")
	flag.IntVar(&expireHours, "expire-hours", 24, "Hours after which to reset speed meter (0 to disable)")
	flag.IntVar(&propConcurrency, "propConcurrency", 60, "Number of concurrent download workers per prop")
	flag.Parse()

	// Validate board type
	if gBoardType == "" {
		golog.Fatal("Board type is required")
	}
	gBoardType = strings.ToUpper(gBoardType)
	PROCESS_STATUS_ID = "watchPhotoDownload" + gBoardType
	if !isValidBoard(gBoardType) {
		golog.Fatalf("Invalid board type: %s", gBoardType)
	}

	// Validate disk type (always required)
	if diskType == "" {
		golog.Fatal("Disk type is required (ca6/ca7)")
	}
	diskType = strings.ToLower(diskType)
	if !isValidDiskType(diskType) {
		golog.Fatalf("Invalid disk type: %s. Valid types: ca6, ca7", diskType)
	}

	// Initialize base[config, logging]
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize collections
	SysdataCol = gomongo.Coll("rni", "sysdata")
	FailedCol = gomongo.Coll("rni", "reso_photo_download_failed")

	// Initialize queue collections based on mode
	if isWatch {
		// Watch mode: initialize dual-write queues (ca6, ca7) + current server's processing queue
		QueueColCa6 = gomongo.Coll("rni", "reso_photo_download_queue_ca6")
		QueueColCa7 = gomongo.Coll("rni", "reso_photo_download_queue_ca7")

		// Also initialize current server's processing queue
		queueCollectionName := getQueueCollectionName(diskType)
		QueueCol = gomongo.Coll("rni", queueCollectionName)

		golog.Info("Watch mode: initialized dual-write + processing queues",
			"ca6Queue", "reso_photo_download_queue_ca6",
			"ca7Queue", "reso_photo_download_queue_ca7",
			"processingQueue", queueCollectionName,
			"diskType", diskType)
	} else {
		// Processing-only mode: initialize single queue based on disk type
		queueCollectionName := getQueueCollectionName(diskType)
		QueueCol = gomongo.Coll("rni", queueCollectionName)
		golog.Info("Processing-only mode: using single queue collection",
			"diskType", diskType,
			"queueCollection", queueCollectionName)
	}

	gUpdateSysData = gowatch.GetUpdateSysdataFunction(SysdataCol, PROCESS_STATUS_ID)

	// Initialize ProcessMonitor
	var err error
	gProcessMonitor, err = goprocess.NewProcessMonitor(goprocess.ProcessMonitorOptions{
		ProcessStatusCol: gomongo.Coll("rni", "processStatus"),
		UpdateInterval:   UPDATE_PROCESS_STATE_INTERVAL,
		Multiplier:       ProcessMonitorMultiplier, // Allow 50% more time than the update interval
		ID:               PROCESS_STATUS_ID,
	})
	if err != nil {
		golog.Fatalf("Failed to initialize ProcessMonitor: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

// startSpeedMeterResetTimer starts a timer that periodically resets the speed meter
func startSpeedMeterResetTimer(ctx context.Context) {
	if expireHours <= 0 {
		golog.Info("Speed meter auto-reset disabled", "expire-hours", expireHours)
		return
	}

	golog.Info("Starting speed meter reset timer", "interval-hours", expireHours)
	ticker := time.NewTicker(time.Duration(expireHours) * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 输出当前统计信息
			stats := speedMeter.ToString(gospeedmeter.UnitH, nil)
			golog.Info("Speed meter stats before reset", "stats", stats)

			// 重置速度计
			speedMeter.Reset()
			golog.Info("Speed meter reset completed")

		case <-ctx.Done():
			golog.Info("Speed meter reset timer stopped")
			return
		}
	}
}

func main() {
	golog.Info("main", "board", gBoardType, "force", force, "dryRun", dryRun, "propConcurrency", propConcurrency)

	// Set up signal handling
	signalChannel := GetSignalChan()
	ctx, cancelCtx := context.WithCancel(context.Background())
	watchCtx, watchCancel := context.WithCancel(context.Background())
	queueCtx, queueCancel := context.WithCancel(context.Background())

	// Handle signals
	go func() {
		sig := <-signalChannel
		sigName := "SIG" + strings.ToUpper(strings.TrimPrefix(sig.String(), "SIG"))
		errMsg := fmt.Sprintf("%s received %s", PROCESS_STATUS_ID, sigName)
		golog.Error(errMsg)

		// First cancel queue processing to stop accepting new tasks
		golog.Info("Cancelling queue processing to stop accepting new tasks")
		queueCancel()

		// Then perform graceful exit, waiting for existing tasks to complete
		GracefulExit(fmt.Errorf("signal %s received", sigName))

		signal.Stop(signalChannel)
		close(signalChannel)
		cancelCtx()
	}()

	// Check for running process
	isRunning, err := gProcessMonitor.CheckRunningProcess()
	if err != nil {
		if !strings.Contains(err.Error(), "no documents") {
			golog.Error("Failed to check running process",
				"error", err,
				"error_type", fmt.Sprintf("%T", err),
				"error_string", err.Error(),
				"process_id", PROCESS_STATUS_ID)
			GracefulExit(fmt.Errorf("failed to check running process: %v", err))
		}
		golog.Info("No existing process found, starting fresh", "process_id", PROCESS_STATUS_ID)
	}
	if isRunning && !dryRun {
		golog.Warn("Process is already running", "process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf(goprocess.HasRunningProcess))
	}

	// Create components
	if err := createComponents(); err != nil {
		golog.Error("Failed to create components", "error", err.Error(), "process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf("failed to create components: %v", err))
	}

	// Start speed meter reset timer
	go startSpeedMeterResetTimer(ctx)

	// Start memory monitoring
	go monitorMemoryUsage(queueCtx)

	// Start HTTP connection cleanup
	go cleanupHTTPConnections(queueCtx)

	// Start queue processor
	go ProcessQueue(queueCtx)

	// Update process status
	startTs := time.Now()
	if err := UpdateProcessStatus(startTs); err != nil {
		golog.Error("Failed to update process status",
			"error", err,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error(),
			"process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf("failed to update process status: %v", err))
	}

	// Start watch functionality if enabled
	if isWatch {
		golog.Info("Watch functionality enabled, starting watch process", "processId", PROCESS_STATUS_ID)

		// Get last resume token
		var sysData bson.M
		err = SysdataCol.FindOne(
			context.Background(),
			bson.M{"_id": PROCESS_STATUS_ID},
		).Decode(&sysData)

		if err != nil {
			if err.Error() == "mongo: no documents in result" {
				golog.Info("No existing sysdata found, starting fresh", "processId", PROCESS_STATUS_ID)
				if err := GetTokenAndWatch(watchCtx, watchCancel); err != nil {
					GracefulExit(err)
				}
			} else {
				golog.Error("Failed to get sysdata", "error", err.Error(), "processId", PROCESS_STATUS_ID)
				GracefulExit(err)
			}
		} else {
			if err := handleTokenAndWatch(watchCtx, watchCancel, sysData, startTs); err != nil {
				GracefulExit(err)
			}
		}
	} else {
		golog.Info("Watch functionality disabled, running in queue processing only mode", "processId", PROCESS_STATUS_ID)
	}

	// Wait for context to be cancelled
	<-ctx.Done()
	// Clean up ProcessMonitor
	if gProcessMonitor != nil {
		gProcessMonitor.StopMonitoring()
	}
}

// SetBoardType sets the board type for testing
func SetBoardType(boardType string) {
	gBoardType = boardType
}

// GetDownloader returns the downloader instance for testing
func GetDownloader() *goresodownload.Downloader {
	return downloader
}

package main

import (
	"fmt"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
)

// isValidBoard checks if the provided board type is valid
func isValidBoard(board string) bool {
	_, exists := goresodownload.BoardMergedTable[board]
	return exists
}

// isValidDiskType checks if the provided disk type is valid
func isValidDiskType(diskType string) bool {
	validDiskTypes := map[string]bool{
		"ca6": true,
		"ca7": true,
	}
	return validDiskTypes[diskType]
}

// getQueueCollectionName returns the queue collection name based on disk type
func getQueueCollectionName(diskType string) string {
	queueNameMap := map[string]string{
		"ca6": "reso_photo_download_queue_ca6",
		"ca7": "reso_photo_download_queue_ca7",
	}

	if queueName, exists := queueNameMap[diskType]; exists {
		return queueName
	}

	// Fallback to default queue name (should not happen with proper validation)
	return "reso_photo_download_queue"
}

// createComponents creates and returns the analyzer, downloader, dirStore, and queue components
func createComponents() error {
	golog.Info("Creating components", "boardType", gBoardType)

	// Create analyzer
	golog.Debug("Creating analyzer")
	analyzer = goresodownload.NewMediaDiffAnalyzer()

	// Create inconsistent data manager
	golog.Debug("Creating inconsistent data manager")
	inconsistentManager = goresodownload.NewInconsistentDataManager()
	golog.Debug("Inconsistent data manager created successfully")

	// Create downloader
	golog.Debug("Creating downloader")
	var err error
	downloader, err = createDownloader()
	if err != nil {
		golog.Error("Failed to create downloader", "error", err)
		return fmt.Errorf("failed to create downloader: %w", err)
	}
	golog.Debug("Downloader created successfully")

	// Create dirStore
	golog.Debug("Creating dirStore")
	collection := gomongo.Coll("rni", "file_server")
	backend := levelStore.NewMongoBackend(collection)
	dirStore, err = levelStore.NewDirKeyStore(gBoardType, backend, "")
	if err != nil {
		golog.Error("Failed to create dirKeyStore", "error", err)
		return fmt.Errorf("failed to create dirKeyStore: %w", err)
	}
	if dirStore == nil {
		golog.Error("dirKeyStore is nil")
		return fmt.Errorf("failed to create dirKeyStore")
	}
	golog.Debug("DirStore created successfully")

	// Create download queue(s) based on mode
	if isWatch {
		// Watch mode: create dual-write queues + processing queue
		golog.Debug("Creating dual-write + processing download queues for watch mode")

		// Create dual-write queues
		downloadQueueCa6, err = goresodownload.NewResourceDownloadQueue(QueueColCa6)
		if err != nil {
			golog.Error("Failed to create ca6 download queue", "error", err)
			return fmt.Errorf("failed to create ca6 download queue: %w", err)
		}

		downloadQueueCa7, err = goresodownload.NewResourceDownloadQueue(QueueColCa7)
		if err != nil {
			golog.Error("Failed to create ca7 download queue", "error", err)
			return fmt.Errorf("failed to create ca7 download queue: %w", err)
		}

		// Create processing queue for current server
		downloadQueue, err = goresodownload.NewResourceDownloadQueue(QueueCol)
		if err != nil {
			golog.Error("Failed to create processing download queue", "error", err)
			return fmt.Errorf("failed to create processing download queue: %w", err)
		}

		golog.Debug("All download queues created successfully for watch mode")
	} else {
		// Processing-only mode: create single queue
		golog.Debug("Creating single download queue for processing-only mode")
		downloadQueue, err = goresodownload.NewResourceDownloadQueue(QueueCol)
		if err != nil {
			golog.Error("Failed to create download queue", "error", err)
			return fmt.Errorf("failed to create download queue: %w", err)
		}
		golog.Debug("Download queue created successfully")
	}

	golog.Info("All components created successfully")
	return nil
}

// createDownloader creates a new downloader instance
func createDownloader() (*goresodownload.Downloader, error) {
	// Get image directories from config
	storagePaths, err := levelStore.GetImageDir(gBoardType)
	if err != nil {
		return nil, fmt.Errorf("failed to get image directories: %w", err)
	}
	if len(storagePaths) == 0 {
		return nil, fmt.Errorf("no image directories configured for board %s", gBoardType)
	}

	// Create downloader options
	opts := &goresodownload.DownloaderOptions{
		Config:       goresodownload.NewDefaultConfig(),
		StoragePaths: storagePaths,
		MergedCol:    gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType]),
		FailedCol:    FailedCol,
		SpeedMeter:   speedMeter,
	}

	// Override PropConcurrency from CLI if provided
	if propConcurrency > 0 {
		opts.Config.PropConcurrency = propConcurrency
		golog.Info("Using PropConcurrency from CLI", "propConcurrency", propConcurrency)
	}

	return goresodownload.NewDownloader(opts)
}

/*
###
Description:    Batch fix existing phoP values by removing a leading '/'

Usage:         ./start.sh  -n fixPhoP -d "goresodownload" -cmd "cmd/batch/fixPhoP/main.go -board=TRB -dryrun"

Create date:    2025-08-12
Author:         Maggie
Run frequency:  One-off
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"sync"
	"time"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for merged collection selection")
	speedMeter *gospeedmeter.SpeedMeter
	speedMutex sync.Mutex
	startTime  = time.Now()
)

// BoardMergedTable maps board types to their merged collection names
var BoardMergedTable = map[string]string{
	"CAR": "mls_car_master_records",
	"DDF": "reso_crea_merged",
	"BRE": "bridge_bcre_merged",
	"EDM": "mls_rae_master_records",
	"TRB": "reso_treb_evow_merged",
}

func init() {
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

func processItem(ctx context.Context, coll *gomongo.MongoCollection, item interface{}) error {
	speedMeter.Check("processed", 1)

	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		data, err := bson.Marshal(v)
		if err != nil {
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		speedMeter.Check("errors", 1)
		return fmt.Errorf("unsupported document type: %T", item)
	}

	id, _ := doc["_id"].(string)
	phoP, ok := doc["phoP"].(string)
	if !ok || phoP == "" {
		speedMeter.Check("skipped_no_phoP", 1)
		return nil
	}

	normalized := goresodownload.NormalizePhoP(phoP)
	if normalized == phoP {
		speedMeter.Check("skipped_already_normalized", 1)
		return nil
	}

	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		golog.Info("Dry run: would update phoP", "_id", id, "old", phoP, "new", normalized)
		return nil
	}

	update := bson.M{"$set": bson.M{"phoP": normalized}}
	_, err := coll.UpdateOne(ctx, bson.M{"_id": id}, update)
	if err != nil {
		speedMeter.Check("updateErrors", 1)
		return fmt.Errorf("failed to update document: %w", err)
	}
	speedMeter.Check("updated", 1)
	golog.Info("Updated phoP", "_id", id, "old", phoP, "new", normalized)
	return nil
}

func run(ctx context.Context) error {
	flag.Parse()
	collName, exists := BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s", *boardFlag)
	}

	coll := gomongo.Coll("rni", collName)
	projection := bson.D{{Key: "_id", Value: 1}, {Key: "phoP", Value: 1}}
	cursor, err := coll.Find(ctx, bson.M{"phoP": bson.M{"$type": "string"}}, gomongo.QueryOptions{Projection: projection})
	if err != nil {
		return fmt.Errorf("failed to execute query: %v", err)
	}

	opts := gostreaming.StreamingOptions{
		Stream:  cursor,
		Process: func(item interface{}) error { return processItem(ctx, coll, item) },
		End: func(err error) {
			duration := time.Since(startTime)
			speedMutex.Lock()
			if err != nil {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed", "duration", duration.String(), "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
			speedMutex.Unlock()
		},
		Error:   func(err error) { golog.Error("Processing error", "error", err) },
		High:    10,
		Verbose: 2,
	}
	return gostreaming.Streaming(ctx, &opts)
}

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	golog.Info("Starting fixPhoP batch")
	if err := run(ctx); err != nil {
		golog.Fatal("fixPhoP failed", "error", err)
	}
	golog.Info("fixPhoP completed successfully")
}

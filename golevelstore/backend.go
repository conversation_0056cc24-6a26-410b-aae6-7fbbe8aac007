package levelStore

import (
	"context"
	"errors"
)

// ErrNotFound is returned by backends when a record does not exist
var ErrNotFound = errors.New("not found")

// L1Record aggregates per-L1 data stored in the backend
// This mirrors fields used by DirKeyStore when persisting stats and dir arrays
// Note: DirStats is defined in dir_key_store.go within the same package
// so we can reference it directly here.
type L1Record struct {
	DirArray      []string
	L2Stats       map[string]DirStats
	TotalFiles    int
	TotalEntities int
}

// DirStoreBackend abstracts storage for DirKeyStore so the store has no
// dependency on MongoDB-specific query/update semantics.
//
// Semantics:
// - Load* returns ErrNotFound if the key does not exist
// - Save* performs an upsert/replace of the record
// - All methods should be safe for concurrent use
// - Context should be honored for cancellation/timeouts
//
// Keying model:
// - boardKey is the resolved board identifier used by DirKeyStore (prefix or prefix_entry)
// - l1 is the first-level directory key
//
type DirStoreBackend interface {
	LoadCurrentL1(ctx context.Context, boardKey string) (string, error)
	SaveCurrentL1(ctx context.Context, boardKey, l1 string) error

	LoadL1(ctx context.Context, boardKey, l1 string) (L1Record, error)
	SaveL1(ctx context.Context, boardKey, l1 string, rec L1Record) error
}


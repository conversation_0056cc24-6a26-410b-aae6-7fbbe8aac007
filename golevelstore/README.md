# GoLevelStore

GoLevelStore is a Go package that provides hierarchical directory management and statistics tracking for file storage systems. It implements a two-level directory structure (L1/L2) with automatic statistics collection and pluggable backend storage.

The package supports both stats-based and time-based L1 directory allocation. Stats-based L1 management provides intelligent directory allocation starting from "100" with automatic upgrades based on file count, while time-based L1 calculation maintains backward compatibility for existing systems.

## Architecture

GoLevelStore uses a **pluggable backend architecture** that separates business logic from storage implementation:

- **DirKeyStore**: Core business logic for directory management and statistics
- **DirStoreBackend Interface**: Pluggable storage backend interface
- **MongoBackend**: MongoDB implementation of the storage backend
- **Future Backends**: Easy to add file-based, memory-based, or other storage backends

## Features

### Core Features
- **Hierarchical Directory Structure**: Two-level directory organization (L1/L2) based on timestamps and property IDs
- **Dual L1 Management**: Supports both stats-based and time-based L1 directory allocation
- **Stats-Based L1**: Intelligent L1 allocation starting from "100" with automatic upgrades when file count exceeds 1,000,000
- **Time-Based L1**: Traditional year-week based L1 calculation for backward compatibility
- **Multi-Board Support**: Each board maintains independent L1 state and configuration
- **Universal Board Support**: Works with any board type, not just image-related boards
- **Custom Directory Support**: Allows specifying custom directories for statistics files
- **Automatic Statistics Tracking**: Real-time directory statistics collection with periodic persistence
- **Background Processing**: Automatic background tasks for statistics saving and cleanup
- **Thread-Safe Operations**: Concurrent-safe operations with proper synchronization
- **Configurable Intervals**: Customizable update and cleanup intervals
- **Board-Specific Configuration**: Support for multiple board types with different L2 sizes

### Backend Architecture
- **Pluggable Storage Backends**: Clean separation between business logic and storage implementation
- **MongoDB Backend**: Full-featured MongoDB integration with optimized queries
- **Backend Interface**: Simple 4-method interface for easy extension
- **Future-Proof Design**: Easy to add file-based, memory-based, or cloud storage backends
- **Unified Error Handling**: Consistent error handling across all backend implementations
- **L1 State Persistence**: L1 states persisted through backend interface for consistency across application restarts

## Installation

```bash
go get github.com/real-rm/gofile/golevelstore
```

## Quick Start

### Using MongoDB Backend (Default)

```go
package main

import (
    "context"
    "log"
    "time"

    levelStore "github.com/real-rm/gofile/golevelstore"
    gomongo "github.com/real-rm/gomongo"
)

func main() {
    // Create MongoDB collection
    collection := gomongo.Coll("rni", "dir_stats")

    // Create MongoDB backend
    backend := levelStore.NewMongoBackend(collection)

    // Create DirKeyStore with MongoDB backend
    store, err := levelStore.NewDirKeyStore("TRB", backend, "", 5*time.Minute)
    if err != nil {
        log.Fatal(err)
    }
    defer store.Close()

    // Add directory statistics
    store.AddDirStats("1225", "abc12", 10, 50) // 10 entities, 50 files

    // Get file path for a property
    path, err := levelStore.GetFullFilePathForProp(time.Now(), "TRB", "********")
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("File path: %s", path) // Output: /1225/abc12

    // For custom applications, you can set custom directories for statistics files
    store.SetCustomDirectories([]string{"/path/to/storage/dir"})
}
```

### Backend Architecture Usage

```go
// You can easily switch between different backends:

// MongoDB backend
mongoBackend := levelStore.NewMongoBackend(collection)
store1, err := levelStore.NewDirKeyStore("TRB", mongoBackend, "", 5*time.Minute)

// Future: File-based backend
// fileBackend := levelStore.NewFileBackend("/data/levelstore")
// store2, err := levelStore.NewDirKeyStore("TRB", fileBackend, "", 5*time.Minute)

// Future: Memory backend for testing
// memoryBackend := levelStore.NewMemoryBackend()
// store3, err := levelStore.NewDirKeyStore("TRB", memoryBackend, "", 5*time.Minute)
```

## Directory Structure

The package uses a two-level hierarchical directory structure:

### L1 Directories

The system supports two L1 calculation methods:

**Stats-Based L1 (Default for new boards):**
- L1 directories start from "100" and increment automatically
- Each board maintains independent L1 state
- L1 upgrades when file count reaches 1,000,000 files per L1
- L1 state persisted in MongoDB for consistency across restarts
- Examples: `100` → `101` → `102` → `103`...

**Time-Based L1 (Legacy support):**
- Based on year and ISO week number
- Format: `YYWW` where YY is year offset from 2015, WW is week number
- Examples:
  - 2015 Week 1 → `750` (base year)
  - 2024 Week 25 → `1225` (750 + 9*50 + 24)
  - 2025 Week 1 → `1250` (750 + 10*50)

**Board Configuration:**
The following boards use time-based L1: TRB, CAR, DDF, BRE, CLG, OTW, EDM, USER
All other boards use stats-based L1.

### L2 Directories (UUID5 Based)
- Generated using UUID5 algorithm with CRC32 hashing
- Property ID (sid) is hashed to determine L2 directory
- Each board type has different L2 array sizes:
  - TRB: 512 directories
  - CAR: 256 directories
  - DDF: 1024 directories
  - BRE/CLG/OTW/EDM: 64 directories

## Backend Architecture

GoLevelStore uses a **pluggable backend architecture** that cleanly separates business logic from storage implementation. This design makes it easy to support different storage systems while maintaining a consistent API.

### Design Principles

1. **Separation of Concerns**: Business logic (DirKeyStore) is completely separate from storage logic (backends)
2. **Interface-Based**: All storage operations go through a simple 4-method interface
3. **Pluggable**: Easy to swap between different storage implementations
4. **Future-Proof**: New storage backends can be added without changing existing code
5. **Testable**: Mock backends can be easily created for testing

### Backend Interface

```go
type DirStoreBackend interface {
    LoadCurrentL1(ctx context.Context, boardKey string) (string, error)
    SaveCurrentL1(ctx context.Context, boardKey, l1 string) error
    LoadL1(ctx context.Context, boardKey, l1 string) (L1Record, error)
    SaveL1(ctx context.Context, boardKey, l1 string, rec L1Record) error
}
```

**Method Descriptions:**
- **`LoadCurrentL1`**: Load the current L1 state for a board (returns `ErrNotFound` if not exists)
- **`SaveCurrentL1`**: Save/update the current L1 state for a board
- **`LoadL1`**: Load statistics and metadata for a specific L1 directory (returns `ErrNotFound` if not exists)
- **`SaveL1`**: Save/update statistics and metadata for a specific L1 directory

**Parameter Explanation:**
- **`boardKey`**: The board identifier, which is `prefix` for traditional boards or `prefix_entryName` for multi-entry boards
- **`l1`**: The L1 directory name (e.g., "100", "101", "1225")
- **`rec`**: The L1Record containing statistics and directory array data

### Available Backends

#### MongoBackend
Full-featured MongoDB implementation with optimized queries:

```go
// Create MongoDB backend
collection := gomongo.Coll("rni", "dir_stats")
backend := levelStore.NewMongoBackend(collection)

// Use with DirKeyStore
store, err := levelStore.NewDirKeyStore("TRB", backend, "", 5*time.Minute)
```

**Features:**
- Optimized MongoDB queries with proper indexing
- Atomic updates using `$set` operations
- Efficient document structure with BSON tags
- Full compatibility with existing MongoDB data

#### Future Backends
The architecture makes it easy to add new backends:

```go
// File-based backend (future)
fileBackend := levelStore.NewFileBackend("/data/levelstore")
store, err := levelStore.NewDirKeyStore("TRB", fileBackend, "", 5*time.Minute)

// Memory backend for testing (future)
memoryBackend := levelStore.NewMemoryBackend()
store, err := levelStore.NewDirKeyStore("TRB", memoryBackend, "", 5*time.Minute)

// Redis backend (future)
redisBackend := levelStore.NewRedisBackend("localhost:6379")
store, err := levelStore.NewDirKeyStore("TRB", redisBackend, "", 5*time.Minute)
```

### Board Key Concept

The backend interface uses a `boardKey` parameter to identify different board configurations:

```go
// For traditional boards (no entryName)
boardKey := "TRB"           // Just the prefix

// For multi-entry boards (with entryName)
boardKey := "NEWBOARD_video"  // prefix + "_" + entryName
boardKey := "NEWBOARD_file"   // prefix + "_" + entryName
```

**How DirKeyStore generates boardKey:**
- Traditional boards: `boardKey = prefix` (e.g., "TRB")
- Multi-entry boards: `boardKey = prefix + "_" + entryName` (e.g., "NEWBOARD_video")

This allows different entry types within the same board to maintain separate L1 states and statistics.

### Error Handling

The backend interface uses a unified error handling approach:

```go
// ErrNotFound is returned when a record doesn't exist
var ErrNotFound = errors.New("not found")

// Usage example - checking for missing records
l1, err := backend.LoadCurrentL1(ctx, "TRB")
if errors.Is(err, levelStore.ErrNotFound) {
    // Handle case where no current L1 is set
    l1 = "100" // Use default
}
```

### Migration from Legacy API

The new backend architecture is fully backward compatible. If you're upgrading from the old API:

```go
// Old API (still works but deprecated)
store, err := levelStore.NewDirKeyStore("TRB", mongoCollection, "", 5*time.Minute)

// New API (recommended)
backend := levelStore.NewMongoBackend(mongoCollection)
store, err := levelStore.NewDirKeyStore("TRB", backend, "", 5*time.Minute)
```

## Core Components

### DirKeyStore

The main component for managing directory statistics and metadata.

#### Function Signature

```go
func NewDirKeyStore(prefix string, backend DirStoreBackend, entryName string, updateInterval ...time.Duration) (*DirKeyStore, error)
```

**Parameters:**
- `prefix` (string): Board name (e.g., "TRB", "NEWBOARD")
- `backend` (DirStoreBackend): Storage backend implementation (e.g., MongoBackend)
- `entryName` (string): Entry type for multi-entry boards (use "" for traditional boards)
- `updateInterval` (...time.Duration): **Optional** update interval (defaults to 10 minutes if not provided)

#### Backend Interface

The `DirStoreBackend` interface provides a clean abstraction for storage operations:

```go
type DirStoreBackend interface {
    LoadCurrentL1(ctx context.Context, boardKey string) (string, error)
    SaveCurrentL1(ctx context.Context, boardKey, l1 string) error
    LoadL1(ctx context.Context, boardKey, l1 string) (L1Record, error)
    SaveL1(ctx context.Context, boardKey, l1 string, rec L1Record) error
}
```

**Backend Methods:**
- `LoadCurrentL1`: Load the current L1 state for a board
- `SaveCurrentL1`: Save the current L1 state for a board
- `LoadL1`: Load statistics for a specific L1 directory
- `SaveL1`: Save statistics for a specific L1 directory

#### Usage Examples

```go
// Create MongoDB backend
collection := gomongo.Coll("rni", "dir_stats")
backend := levelStore.NewMongoBackend(collection)

// Create a new DirKeyStore (traditional board without entryName)
store, err := levelStore.NewDirKeyStore("TRB", backend, "", 10*time.Minute)
if err != nil {
    log.Fatal(err)
}

// Create DirKeyStore with entryName for multi-entry boards
videoStore, err := levelStore.NewDirKeyStore("NEWBOARD", backend, "video", 5*time.Minute)
if err != nil {
    log.Fatal(err)
}

// updateInterval is optional - uses default (10 minutes) if not provided
fileStore, err := levelStore.NewDirKeyStore("NEWBOARD", backend, "file")
if err != nil {
    log.Fatal(err)
}

// Multiple ways to call NewDirKeyStore:
// 1. Traditional board with custom interval
store1, err := levelStore.NewDirKeyStore("TRB", backend, "", 5*time.Minute)
if err != nil {
    log.Fatal(err)
}
defer store1.Close()

// 2. Traditional board with default interval
store2, err := levelStore.NewDirKeyStore("TRB", backend, "")
if err != nil {
    log.Fatal(err)
}
defer store2.Close()

// 3. Multi-entry board with custom interval
store3, err := levelStore.NewDirKeyStore("NEWBOARD", backend, "audio", 15*time.Minute)
if err != nil {
    log.Fatal(err)
}
defer store3.Close()

// 4. Multi-entry board with default interval
store4, err := levelStore.NewDirKeyStore("NEWBOARD", backend, "document")
if err != nil {
    log.Fatal(err)
}
defer store4.Close()

// Add statistics for a specific L1/L2 combination
store.AddDirStats("1225", "abc12", entityCount, fileCount)

// For stats-based boards, L1 automatically upgrades when file count reaches 1,000,000
// The upgrade happens during statistics processing

// Manually save current statistics
store.SaveChangedCurrent()

// For non-image boards or custom applications, you can specify custom directories
// for storing statistics files
store.SetCustomDirectories([]string{"/path/to/custom/storage"})
```

#### Creating Custom Backends

You can implement your own storage backend by implementing the `DirStoreBackend` interface:

```go
type MyCustomBackend struct {
    // Your storage implementation
}

func (b *MyCustomBackend) LoadCurrentL1(ctx context.Context, boardKey string) (string, error) {
    // Load current L1 state from your storage
    // boardKey format: "TRB" or "NEWBOARD_video"
    return "", levelStore.ErrNotFound // Return ErrNotFound if not exists
}

func (b *MyCustomBackend) SaveCurrentL1(ctx context.Context, boardKey, l1 string) error {
    // Save current L1 state to your storage
    // boardKey format: "TRB" or "NEWBOARD_video"
    // l1 format: "100", "101", "1225", etc.
    return nil
}

func (b *MyCustomBackend) LoadL1(ctx context.Context, boardKey, l1 string) (levelStore.L1Record, error) {
    // Load L1 statistics from your storage
    return levelStore.L1Record{}, levelStore.ErrNotFound // Return ErrNotFound if not exists
}

func (b *MyCustomBackend) SaveL1(ctx context.Context, boardKey, l1 string, rec levelStore.L1Record) error {
    // Save L1 statistics to your storage
    return nil
}

// Use your custom backend
customBackend := &MyCustomBackend{}
store, err := levelStore.NewDirKeyStore("TRB", customBackend, "", 5*time.Minute)
```

### Using Custom Directory Support

For applications that manage their own storage directories:

```go
// Get storage directories from application config
localStorageDirs := []string{
    "/app/data/uploads/files",
    "/app/data/backups/files",
}

// Create DirKeyStore
store, err := levelStore.NewDirKeyStore("CUSTOM_BOARD", mongoCollection, "")
if err != nil {
    log.Fatal(err)
}

// Set custom directories for statistics files
// This will store dir_stats.json files in these directories
store.SetCustomDirectories(localStorageDirs)

// Add statistics as usual
store.AddDirStats("100", "abc12", 1, 1)
```

### L1 Calculation

The package supports both stats-based and time-based L1 calculation:

```go
// Time-based L1 (for TRB, CAR, DDF, BRE, CLG, OTW, EDM, USER)
trbL1 := levelStore.CalculateL1(20241225, "TRB") // Returns "1250" (time-based)

// Stats-based L1 (for other boards)
newBoardL1 := levelStore.CalculateL1(20241225, "NEWBOARD") // Returns "100" or current L1

// Stats-based L1 automatically upgrades based on file statistics
// When files in current L1 >= 1,000,000, L1 upgrades to next number
// "100" → "101" → "102" → "103" → ...

// Each board maintains independent L1 state
// L1 state is persisted in MongoDB and survives application restarts
```

### Path Calculation Functions

```go
// Get complete file path
path, err := levelStore.GetFullFilePathForProp(timestamp, "TRB", "********")
// Returns: "/1225/abc12" (time-based for TRB)

```

### User Upload Configuration

The package provides `GetUserUploadConfig` function to retrieve user upload configurations for different file types and boards, following the same pattern as `GetImageDir`.

```go
// Get user upload configuration for file entryName
config, err := levelStore.GetUserUploadConfig("rm", "file", nil)
if err != nil {
    log.Fatal(err)
}

// Use configuration for upload processing
uploadURL := fmt.Sprintf("f.realmaster.com%s/l1/l2/123.mp3", config.Prefix)
tempFile := filepath.Join(config.TmpPath, "processing", "file.tmp")
draftFile := filepath.Join(config.DraftPath, "user123", "draft.mp3")

// Use local storage paths
for _, localPath := range config.LocalPath {
    finalPath := filepath.Join(localPath, "l1", "l2", "123.mp3")
    // Save to local storage
}

// Use S3 configurations
for _, s3Config := range config.S3Path {
    // Upload to S3 using s3Config.Endpoint, s3Config.Bucket, etc.
}
```

#### User Upload Configuration Structure

```go
// Connection sources configuration
type S3ProviderConfig struct {
    Name     string `json:"name"`     // Provider name (e.g., "aws-primary-storage")
    Endpoint string `json:"endpoint"` // S3 endpoint URL
    Key      string `json:"key"`      // Access key
    Pass     string `json:"pass"`     // Secret key
    Region   string `json:"region"`   // AWS region (optional)
}

type ConnectionSources struct {
    S3Providers []S3ProviderConfig `json:"s3_providers"`
}

// Storage configuration
type StorageConfig struct {
    Type   string `json:"type"`   // "local" or "s3"
    Path   string `json:"path"`   // for local storage
    Target string `json:"target"` // for s3 storage (references s3_providers name)
    Bucket string `json:"bucket"` // for s3 storage
}

// User upload configuration
type UserUploadConfig struct {
    EntryName string          `json:"entryName"` // Entry name (file, video, log_archive, etc.)
    Prefix    string          `json:"prefix"`    // URL prefix for uploads
    TmpPath   string          `json:"tmpPath"`   // Temporary processing path
    MaxSize   string          `json:"maxSize"`   // Maximum file size (e.g., "500MB")
    DraftPath string          `json:"draftPath"` // Draft storage path
    Storage   []StorageConfig `json:"storage"`   // Storage configurations (new format)
    // Deprecated fields for backward compatibility
    LocalPath []string   `json:"localpath,omitempty"` // Legacy local storage paths
    S3Path    []S3Config `json:"s3path,omitempty"`    // Legacy S3 storage configurations
}
```

#### Configuration Format

The function expects configuration in this format:

```toml
# Connection sources configuration (S3 providers, etc.)
[connection_sources]

  [[connection_sources.s3_providers]]
    name = "aws-primary-storage"
    endpoint = "s3.us-east-1.amazonaws.com"
    key = "PRIMARY_AWS_KEY"
    pass = "PRIMARY_AWS_SECRET"
    region = "us-east-1"

  [[connection_sources.s3_providers]]
    name = "aws-backup-storage"
    endpoint = "s3.eu-central-1.amazonaws.com"
    key = "BACKUP_AWS_KEY"
    pass = "BACKUP_AWS_SECRET"
    region = "eu-central-1"

  [[connection_sources.s3_providers]]
    name = "minio-internal-dev"
    endpoint = "minio.internal.my-company.com"
    key = "minio_dev_key"
    pass = "minio_dev_secret"

# User upload configuration
[userupload]
site = "rm"

  [[userupload.types]]
    entryName = "log_archive"
    prefix = "/internal/logs"
    tmpPath = "/tmp/goupload/logs"
    maxSize = "500MB"
    storage = [
      { type = "s3", target = "aws-backup-storage", bucket = "my-app-log-archive-bucket" },
      { type = "s3", target = "minio-internal-dev", bucket = "log-backups" }
    ]

  [[userupload.types]]
    entryName = "video"
    prefix = "/videos"
    tmpPath = "/tmp/goupload/videos"
    maxSize = "5GB"
    storage = [
      { type = "local", path = "/data/storage/videos" },
      { type = "s3", target = "aws-primary-storage", bucket = "my-app-videos-bucket" },
      { type = "s3", target = "aws-backup-storage", bucket = "my-app-archive-bucket" }
    ]

  [[userupload.types]]
    entryName = "avatar"
    prefix = "/users/avatars"
    tmpPath = "/tmp/goupload/avatars"
    maxSize = "10MB"
    storage = [
      { type = "local", path = "/data/storage/avatars" },
      { type = "s3", target = "aws-primary-storage", bucket = "my-app-avatars-bucket" }
    ]
```

#### Usage Examples

**Getting Connection Sources:**
```go
// Uses goconfig.Config("connection_sources")
connSources, err := levelStore.GetConnectionSources(nil)
if err != nil {
    log.Fatal(err)
}

// Access S3 providers
for _, provider := range connSources.S3Providers {
    log.Printf("Provider: %s, Endpoint: %s", provider.Name, provider.Endpoint)
}
```

**Getting User Upload Configuration:**
```go
// Uses goconfig.Config("userupload")
config, err := levelStore.GetUserUploadConfig("rm", "log_archive", nil)
if err != nil {
    log.Fatal(err)
}

// Access storage configurations
for _, storage := range config.Storage {
    if storage.Type == "s3" {
        log.Printf("S3 Storage: target=%s, bucket=%s", storage.Target, storage.Bucket)
    } else if storage.Type == "local" {
        log.Printf("Local Storage: path=%s", storage.Path)
    }
}
```

**Using Custom Configuration:**
```go
customConfig := map[string]interface{}{
    "connection_sources": map[string]interface{}{
        "s3_providers": []interface{}{
            map[string]interface{}{
                "name":     "aws-backup-storage",
                "endpoint": "s3.eu-central-1.amazonaws.com",
                "key":      "BACKUP_AWS_KEY",
                "pass":     "BACKUP_AWS_SECRET",
                "region":   "eu-central-1",
            },
        },
    },
    "userupload": map[string]interface{}{
        "site": "rm",
        "types": []interface{}{
            map[string]interface{}{
                "entryName": "log_archive",
                "prefix":    "/internal/logs",
                "tmpPath":   "/tmp/goupload/logs",
                "maxSize":   "500MB",
                "storage": []interface{}{
                    map[string]interface{}{
                        "type":   "s3",
                        "target": "aws-backup-storage",
                        "bucket": "my-app-log-archive-bucket",
                    },
                },
            },
        },
    },
}

// Get connection sources
connSources, err := levelStore.GetConnectionSources(customConfig)

// Get user upload config
config, err := levelStore.GetUserUploadConfig("rm", "log_archive", customConfig)
```

**Multiple Storage Paths:**
```go
// Configuration supports multiple local paths and S3 endpoints
config, err := levelStore.GetUserUploadConfig("rr", "video", nil)

// Access multiple local storage paths
for i, localPath := range config.LocalPath {
    log.Printf("Local storage %d: %s", i, localPath)
}

// Access multiple S3 configurations
for i, s3Config := range config.S3Path {
    log.Printf("S3 endpoint %d: %s/%s", i, s3Config.Endpoint, s3Config.Bucket)
}
```

#### Error Handling

The function validates board and type configurations:

```go
config, err := levelStore.GetUserUploadConfig("rr", "file", nil)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "board"):
        // Handle board validation error
        log.Printf("Board not configured: %v", err)
    case strings.Contains(err.Error(), "upload type"):
        // Handle type validation error
        log.Printf("Upload type not found: %v", err)
    case strings.Contains(err.Error(), "userupload configuration"):
        // Handle configuration error
        log.Printf("Configuration error: %v", err)
    default:
        log.Printf("Unknown error: %v", err)
    }
}
```

## Configuration

### Board Types and L2 Sizes

The package supports multiple board types with different L2 directory sizes and L1 calculation methods:

```go
// Supported board types and their L2 sizes
var BoardImageDirName = map[string]string{
    "CAR": "reso_car_image_dir",     // 256 L2 directories, time-based L1
    "DDF": "reso_crea_image_dir",    // 1024 L2 directories, time-based L1
    "BRE": "reso_bcre_image_dir",    // 64 L2 directories, time-based L1
    "EDM": "reso_edm_image_dir",     // 64 L2 directories, time-based L1
    "TRB": "reso_treb_image_dir",    // 512 L2 directories, time-based L1
}

// L1 Calculation Methods:
// Time-based L1: TRB, CAR, DDF, BRE, CLG, OTW, EDM, USER
// Stats-based L1: All other boards
```

### Image Directory Configuration

The package provides `GetImageDir` function to retrieve configured image directories for different boards:

```go
// Get configured image directories for a board
dirs, err := levelStore.GetImageDir("TRB")
if err != nil {
    log.Fatalf("Failed to get image directories: %v", err)
}

// Handle different scenarios
if dirs == nil {
    log.Info("No directories configured for board TRB")
} else if len(dirs) == 0 {
    log.Info("Empty directory configuration for board TRB")
} else {
    log.Printf("Found %d directories for board TRB: %v", len(dirs), dirs)
}

// Error handling for unsupported boards
dirs, err = levelStore.GetImageDir("INVALID")
if err != nil {
    log.Printf("Error: %v", err) // "unsupported board type: INVALID"
}
```

### Configuration File

The package requires a configuration file (TOML format) with image store settings:

```toml
[imageStore]
reso_treb_image_dir = ["/data/trb_images", "/backup/trb_images"]
reso_car_image_dir = ["/data/car_images"]
reso_crea_image_dir = ["/data/ddf_images"]
reso_bcre_image_dir = ["/data/bre_images"]
reso_edm_image_dir = ["/data/edm_images"]

[source_l2_size]
TRB = 512   # Time-based L1
CAR = 256   # Time-based L1
DDF = 1024  # Time-based L1
BRE = 64    # Time-based L1
CLG = 64    # Time-based L1
OTW = 64    # Time-based L1
EDM = 64    # Time-based L1
USER = 64   # Time-based L1
# Other boards use stats-based L1
```

## Data Structures

### Backend Interface Types

#### L1Record
Core data structure used by backends to store L1 information:

```go
type L1Record struct {
    DirArray      []string                 // L2 directory array (UUID list)
    L2Stats       map[string]DirStats      // Per-L2 statistics
    TotalFiles    int                      // Total files in this L1
    TotalEntities int                      // Total entities in this L1
}
```

**Field Descriptions:**
- **`DirArray`**: Array of L2 directory names (UUIDs) for this L1
- **`L2Stats`**: Map of L2 directory name to statistics (entity count, file count)
- **`TotalFiles`**: Total number of files across all L2 directories in this L1
- **`TotalEntities`**: Total number of entities across all L2 directories in this L1

#### DirStoreBackend Interface
Storage backend interface for pluggable storage implementations:

```go
type DirStoreBackend interface {
    LoadCurrentL1(ctx context.Context, prefix, entryName string) (*L1Record, error)
    SaveCurrentL1(ctx context.Context, prefix, entryName string, record *L1Record) error
    LoadL1(ctx context.Context, prefix, entryName, l1 string) (*L1Record, error)
    SaveL1(ctx context.Context, prefix, entryName, l1 string, record *L1Record) error
}
```

### Application Data Structures

#### DirStats
Tracks statistics for individual directories:

```go
type DirStats struct {
    EntityAmount int `bson:"l" json:"entity_amount"` // Number of entities
    FileAmount   int `bson:"f" json:"file_amount"`   // Number of files
}
```

#### DirMetaInfo
Comprehensive metadata for L1 directories:

```go
type DirMetaInfo struct {
    TotalEntities int                 `json:"total_entities"`
    TotalFiles    int                 `json:"total_files"`
    L2Stats       map[string]DirStats `json:"l2_stats"`
    ActualSize    int64               `json:"actual_size"` // Bytes
    DiskSize      int64               `json:"disk_size"`   // Disk usage
    LastUpdated   time.Time           `json:"last_updated"`
}
```

#### PathResult
Result structure for path calculations:

```go
type PathResult struct {
    L1       string // L1 directory (e.g., "1225")
    L2       string // L2 directory (e.g., "abc12")
    Combined string // Combined path (e.g., "1225/abc12")
}
```

### UserUploadConfig
Configuration structure for user upload settings:

```go
type UserUploadConfig struct {
    EntryName string     `json:"entryName"` // Entry name (file, video, log_archive, audio, doc, other)
    Prefix    string     `json:"prefix"`    // URL prefix for uploads (e.g., "/useruploads", "/internal/logs")
    TmpPath   string     `json:"tmpPath"`   // Temporary processing path (e.g., "/tmp", "/tmp/goupload/logs")
    MaxSize   string     `json:"maxSize"`   // Maximum file size (e.g., "500MB", "1GB")
    DraftPath string     `json:"draftPath"` // Draft storage path (e.g., "/draft")
    LocalPath []string   `json:"localpath"` // Multiple local storage paths
    S3Path    []S3Config `json:"s3path"`    // Multiple S3 storage configurations
}
```

### S3Config
S3 storage configuration structure:

```go
type S3Config struct {
    Endpoint string `json:"endpoint"` // S3 endpoint URL (e.g., "s3.amazonaws.com")
    Key      string `json:"key"`      // S3 access key ID
    Pass     string `json:"pass"`     // S3 secret access key
    Bucket   string `json:"bucket"`   // S3 bucket name
}
```

## Advanced Usage

### Stats-Based L1 Management

```go
// Monitor current L1 state
store, err := levelStore.NewDirKeyStore("TRB", collection, "", 5*time.Minute)
currentL1 := store.getCurrentL1() // Get current L1 (e.g., "100")

// L1 upgrades happen automatically during statistics processing
// You can monitor upgrade events through logs

// Force statistics processing to trigger L1 upgrade check
store.SaveChangedCurrent()

// L1 upgrade threshold
const L1_UPGRADE_THRESHOLD = 1000000 // 1 million files

// Example: Adding statistics that might trigger upgrade
for i := 0; i < 1000000; i++ {
    store.AddDirStats(currentL1, fmt.Sprintf("dir%d", i%512), 1, 1)
}
// After SaveChangedCurrent(), L1 might upgrade from "100" to "101"
```

### Custom Update Intervals

```go
// Create store with custom intervals
store, err := levelStore.NewDirKeyStore("TRB", collection, "", 5*time.Minute)

// Default intervals
const (
    DEFAULT_UPDATE_INTERVAL = 10 * time.Minute   // Statistics save interval
    DEFAULT_CLEAN_INTERVAL  = 7 * 24 * time.Hour // Cache cleanup interval
)
```

### Directory Array Management

```go
// Fetch L2 directory array for specific L1
dirArray, err := store.FetchDirArrayByL1(ctx, "TRB", "1225")

// Save/update directory array in database
dirArray, err := store.SaveDirArrayByL1(ctx, "TRB", "1225", false) // false = no force update

// Force update existing array
dirArray, err := store.SaveDirArrayByL1(ctx, "TRB", "1225", true) // true = force update
```

### File Listing

```go
// List files in directory with SID prefix matching
files, err := levelStore.ListFiles("/data/files", "1225/abc12", "********")
```

## Background Processing

The DirKeyStore automatically runs background tasks:

1. **Statistics Persistence**: Saves accumulated statistics to MongoDB and files at configured intervals
2. **Cache Cleanup**: Periodically cleans temporary directory mappings to prevent memory leaks
3. **Thread-Safe Operations**: All operations are protected by read-write mutexes

## Error Handling

The package provides comprehensive error handling:

```go
// Check for specific error types
if err != nil {
    if strings.Contains(err.Error(), "no image directories configured") {
        // Handle configuration error
    } else if strings.Contains(err.Error(), "invalid board") {
        // Handle invalid board type
    }
}

// Validate board before operations
if !levelStore.IsValidBoard("TRB") {
    log.Fatal("Invalid board type")
}
```

## Testing

The package includes comprehensive test coverage:

```bash
# Run all tests
go test -v

# Run specific test
go test -v -run TestNewDirKeyStore

# Run tests with coverage
go test -cover
```

### Test Configuration

Tests require a `local.test.ini` configuration file in the package directory:

```toml
[dbs]
verbose = 3

[dbs.tmp]
uri = "***********************************************************"

[imageStore]
reso_treb_image_dir = ["/tmp/trb_images", "/tmp/trb_images2"]
reso_car_image_dir = ["/tmp/car_images"]
# ... other image directories

[source_l2_size]
TRB = 512
CAR = 256
# ... other board sizes
```

## Performance Considerations

- **Memory Usage**: Statistics are accumulated in memory and periodically flushed to reduce I/O
- **Concurrency**: Thread-safe operations with minimal lock contention
- **Caching**: L2 directory arrays are cached to avoid repeated calculations
- **Batch Operations**: Statistics are saved in batches for better performance

## Dependencies

- `github.com/real-rm/goconfig` - Configuration management
- `github.com/real-rm/gohelper` - Helper utilities and intervals
- `github.com/real-rm/golog` - Structured logging
- `github.com/real-rm/gomongo` - MongoDB operations
- `go.mongodb.org/mongo-driver` - MongoDB driver
- `github.com/spaolacci/murmur3` - Murmur3 hashing

## Quick Reference

### Core Functions

| Function | Description | Example |
|----------|-------------|---------|
| `NewDirKeyStore(board, backend, entryName, interval...)` | Create new directory key store | `store, err := NewDirKeyStore("TRB", backend, "", 5*time.Minute)` |
| `NewMongoBackend(collection)` | Create MongoDB backend | `backend := NewMongoBackend(collection)` |
| `GetFullFilePathForProp(time, board, propID)` | Get complete file path | `path, err := GetFullFilePathForProp(time.Now(), "TRB", "********")` |
| `GetL1L2Separate(time, board, propID)` | Get L1 and L2 separately | `l1, l2, err := GetL1L2Separate(time.Now(), "TRB", "********")` |
| `GetPathComponents(time, board, propID)` | Get path components with details | `result, err := GetPathComponents(time.Now(), "TRB", "********")` |
| `GetUserUploadConfig(board, type, cfg)` | Get user upload configuration | `config, err := GetUserUploadConfig("rr", "file", nil)` |
| `CalculateL1(date, board)` | Calculate L1 (stats-based, date for compatibility) | `l1 := CalculateL1(20241225, "TRB")` // Returns current L1 state |
| `CalculateL2(propID, l2Array)` | Calculate L2 from property ID | `l2, err := CalculateL2("********", l2Array)` |

### DirKeyStore Methods

| Method | Description | Example |
|--------|-------------|---------|
| `AddDirStats(l1, l2, entities, files)` | Add directory statistics | `store.AddDirStats("100", "abc12", 10, 50)` |
| `getCurrentL1()` | Get current L1 state for board | `currentL1 := store.getCurrentL1()` // Returns "100", "101", etc. |
| `SaveChangedCurrent()` | Manually save statistics (triggers L1 upgrade check) | `store.SaveChangedCurrent()` |
| `FetchDirArrayByL1(ctx, board, l1)` | Fetch L2 directory array | `dirArray, err := store.FetchDirArrayByL1(ctx, "TRB", "100")` |
| `SaveDirArrayByL1(ctx, board, l1, force)` | Save/update directory array | `dirArray, err := store.SaveDirArrayByL1(ctx, "TRB", "100", false)` |
| `SetSourceRoot(path)` | Set custom source root | `store.SetSourceRoot("/custom/data/files")` |
| `Close()` | Close store and cleanup | `defer store.Close()` |

### Backend Functions

| Function | Description | Return Type |
|----------|-------------|-------------|
| `NewMongoBackend(collection)` | Create MongoDB backend implementation | `DirStoreBackend` |
| `(backend).LoadCurrentL1(ctx, boardKey)` | Load current L1 state | `(string, error)` |
| `(backend).SaveCurrentL1(ctx, boardKey, l1)` | Save current L1 state | `error` |
| `(backend).LoadL1(ctx, boardKey, l1)` | Load L1 statistics | `(L1Record, error)` |
| `(backend).SaveL1(ctx, boardKey, l1, rec)` | Save L1 statistics | `error` |

### Configuration Functions

| Function | Description | Return Type |
|----------|-------------|-------------|
| `GetUserUploadConfig(board, type, cfg)` | Get upload configuration for board/type | `*UserUploadConfig, error` |
| `GetImageDir(board)` | Get image directories for board | `([]string, error)` |
| `getDirsByFileType(type, board, cfg)` | Get directories by file type | `[]string` |

## API Reference

For detailed API documentation, see [api.md](api.md) or use:

```bash
go doc github.com/real-rm/gofile/golevelstore
```

## Examples

### Complete Example with Error Handling

```go
package main

import (
    "context"
    "log"
    "time"

    levelStore "github.com/real-rm/gofile/golevelstore"
    gomongo "github.com/real-rm/gomongo"
)

func main() {
    // Initialize MongoDB connection
    collection := gomongo.Coll("filestore", "directory_stats")
    if collection == nil {
        log.Fatal("Failed to create MongoDB collection")
    }

    // Create MongoDB backend
    backend := levelStore.NewMongoBackend(collection)

    // Create DirKeyStore with backend
    store, err := levelStore.NewDirKeyStore("TRB", backend, "", 5*time.Minute)
    if err != nil {
        log.Fatalf("Failed to create DirKeyStore: %v", err)
    }
    defer store.Close()

    // Set custom source root if needed
    store.SetSourceRoot("/custom/data/files")

    // Get current L1 state (starts at "100" for new boards)
    currentL1 := store.getCurrentL1()
    log.Printf("Current L1 for TRB board: %s", currentL1)

    // Add some statistics using current L1
    store.AddDirStats(currentL1, "abc12", 10, 50)
    store.AddDirStats(currentL1, "def34", 5, 25)

    // Get path components for a property
    result, err := levelStore.GetPathComponents(time.Now(), "TRB", "********")
    if err != nil {
        log.Fatalf("Failed to get path components: %v", err)
    }

    log.Printf("L1: %s, L2: %s, Combined: %s", result.L1, result.L2, result.Combined)

    // Manually save statistics (optional - happens automatically)
    // This also triggers L1 upgrade check if file count threshold is reached
    store.SaveChangedCurrent()

    // Check if L1 has been upgraded after saving statistics
    newL1 := store.getCurrentL1()
    if newL1 != currentL1 {
        log.Printf("L1 upgraded from %s to %s", currentL1, newL1)
    }

    // Fetch directory array for current L1
    ctx := context.Background()
    dirArray, err := store.FetchDirArrayByL1(ctx, "TRB", newL1)
    if err != nil {
        log.Printf("Failed to fetch directory array: %v", err)
    } else {
        log.Printf("Directory array size for L1 %s: %d", newL1, len(dirArray))
    }

    // Get user upload configuration
    uploadConfig, err := levelStore.GetUserUploadConfig("rm", "log_archive", nil)
    if err != nil {
        log.Printf("Failed to get upload config: %v", err)
    } else {
        log.Printf("Upload prefix: %s", uploadConfig.Prefix)
        log.Printf("Temp path: %s", uploadConfig.TmpPath)
        log.Printf("Max size: %s", uploadConfig.MaxSize)
        log.Printf("Draft path: %s", uploadConfig.DraftPath)
        log.Printf("Local paths: %v", uploadConfig.LocalPath)
        log.Printf("S3 storage configs: %d configured", len(uploadConfig.S3Path))

        // Use upload configuration for file processing
        for i, localPath := range uploadConfig.LocalPath {
            finalPath := filepath.Join(localPath, result.Combined, "uploaded_file.jpg")
            log.Printf("Local storage %d: %s", i, finalPath)
        }

        for i, s3Config := range uploadConfig.S3Path {
            log.Printf("S3 storage %d: %s/%s", i, s3Config.Endpoint, s3Config.Bucket)
        }
    }
}
```

## License

This package is part of the gofile project. See the main project for license information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

For bug reports and feature requests, please create an issue in the main gofile repository.
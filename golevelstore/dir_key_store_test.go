package levelStore

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"sync"
	"testing"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	testColl            *gomongo.MongoCollection
	originalGetImageDir = GetImageDir
)

// Helper function to create DirKeyStore with MongoBackend for tests
func newTestDirKeyStore(prefix string, coll *gomongo.MongoCollection, entryName string, updateInterval ...time.Duration) (*DirKeyStore, error) {
	backend := NewMongoBackend(coll)
	return NewDirKeyStore(prefix, backend, entryName, updateInterval...)
}

// SetupTestProcess sets up the test environment
func SetupTestProcess(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	coll := gomongo.Coll("tmp", "processStatus")
	testColl = coll

	if coll == nil {
		t.Error("Failed to create test collection.", "name", "processStatus")
	}
	// Return collection and cleanup function
	return coll, func(coll *gomongo.MongoCollection) {
		if coll == nil {
			t.Log("Collection is nil, skipping cleanup")
			return
		}
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Logf("Failed to cleanup collection: %v", err)
		}
	}
}

func TestNewDirKeyStore(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	tests := []struct {
		name           string
		prefix         string
		coll           interface{}
		updateInterval time.Duration
		wantErr        bool
	}{
		{
			name:           "valid parameters",
			prefix:         "TRB",
			coll:           testColl,
			updateInterval: 5 * time.Minute,
			wantErr:        false,
		},
		{
			name:           "nil collection",
			prefix:         "TRB",
			coll:           nil,
			updateInterval: 5 * time.Minute,
			wantErr:        true,
		},
		{
			name:           "invalid collection type",
			prefix:         "TRB",
			coll:           "invalid",
			updateInterval: 5 * time.Minute,
			wantErr:        true,
		},
		{
			name:           "zero update interval",
			prefix:         "TRB",
			coll:           testColl,
			updateInterval: 0,
			wantErr:        false, // Should use default interval
		},
		{
			name:           "negative update interval",
			prefix:         "TRB",
			coll:           testColl,
			updateInterval: -1 * time.Minute,
			wantErr:        false, // Should use default interval
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var backend DirStoreBackend
			if tt.coll != nil {
				if mongoCollection, ok := tt.coll.(*gomongo.MongoCollection); ok {
					backend = NewMongoBackend(mongoCollection)
				} else if dirBackend, ok := tt.coll.(DirStoreBackend); ok {
					backend = dirBackend
				} else {
					// For invalid types, pass the original value to NewDirKeyStore to test error handling
					store, err := NewDirKeyStore(tt.prefix, tt.coll, "", tt.updateInterval)
					if tt.wantErr {
						assert.Error(t, err)
						assert.Nil(t, store)
						return
					}
					assert.NoError(t, err)
					assert.NotNil(t, store)
					defer store.Close()
					return
				}
			}
			store, err := NewDirKeyStore(tt.prefix, backend, "", tt.updateInterval)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, store)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, store)
			defer store.Close()

			// Verify that store was created with correct parameters
			assert.Equal(t, tt.prefix, store.prefix)
		})
	}
}

func TestDirKeyStore_AddDirStats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	backend := NewMongoBackend(coll)
	store, err := NewDirKeyStore("TRB", backend, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name           string
		l1             string
		l2             string
		entityAmount   int
		fileAmount     int
		expectedEntity int
		expectedFile   int
	}{
		{
			name:           "add positive stats",
			l1:             "750",
			l2:             "abc12",
			entityAmount:   10,
			fileAmount:     5,
			expectedEntity: 10,
			expectedFile:   5,
		},
		{
			name:           "add negative stats",
			l1:             "750",
			l2:             "abc12",
			entityAmount:   -5,
			fileAmount:     -2,
			expectedEntity: 5, // 10 + (-5)
			expectedFile:   3, // 5 + (-2)
		},
		{
			name:           "add zero stats",
			l1:             "750",
			l2:             "abc12",
			entityAmount:   0,
			fileAmount:     0,
			expectedEntity: 5, // Previous value remains
			expectedFile:   3, // Previous value remains
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.AddDirStats(tt.l1, tt.l2, tt.entityAmount, tt.fileAmount)
			key := tt.l1 + "-" + tt.l2
			stats, exists := store.dirMap[key]
			assert.True(t, exists)
			assert.Equal(t, tt.expectedEntity, stats.EntityAmount)
			assert.Equal(t, tt.expectedFile, stats.FileAmount)
		})
	}
}

func TestDirKeyStore_SaveChangedCurrent(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add test data
	store.AddDirStats("750", "abc12", 10, 5)
	store.AddDirStats("750", "def34", 20, 10)
	store.AddDirStats("751", "ghi56", 30, 15)

	// Save to database
	err = store.saveStatsToDb(map[string]map[string]DirStats{
		"750": {
			"abc12": {EntityAmount: 10, FileAmount: 5},
			"def34": {EntityAmount: 20, FileAmount: 10},
		},
		"751": {
			"ghi56": {EntityAmount: 30, FileAmount: 15},
		},
	})
	assert.NoError(t, err)

	// Verify data in database
	ctx := context.Background()
	var result struct {
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}

	// Check L1 750
	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "750"},
		}},
	}).Decode(&result)
	assert.NoError(t, err)
	assert.Equal(t, 15, result.TotalFiles)    // 5 + 10
	assert.Equal(t, 30, result.TotalEntities) // 10 + 20
	assert.Equal(t, 5, result.L2Stats["abc12"].FileAmount)
	assert.Equal(t, 10, result.L2Stats["def34"].FileAmount)
	assert.Equal(t, 10, result.L2Stats["abc12"].EntityAmount)
	assert.Equal(t, 20, result.L2Stats["def34"].EntityAmount)

	// Check L1 751
	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "751"},
		}},
	}).Decode(&result)
	assert.NoError(t, err)
	assert.Equal(t, 15, result.TotalFiles)
	assert.Equal(t, 30, result.TotalEntities)
	assert.Equal(t, 15, result.L2Stats["ghi56"].FileAmount)
	assert.Equal(t, 30, result.L2Stats["ghi56"].EntityAmount)
}

func TestDirKeyStore_AddTmpDirMap(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name     string
		l1       string
		dirArray []string
	}{
		{
			name:     "add valid array",
			l1:       "750",
			dirArray: []string{"abc12", "def34", "ghi56"},
		},
		{
			name:     "add empty array",
			l1:       "751",
			dirArray: []string{},
		},
		{
			name:     "add nil array",
			l1:       "752",
			dirArray: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.AddTmpDirMap(tt.l1, tt.dirArray)
			value, exists := store.tmpDirMap.Load(tt.l1)
			assert.True(t, exists)
			if tt.dirArray != nil {
				assert.Equal(t, tt.dirArray, value)
			} else {
				assert.Nil(t, value)
			}
		})
	}
}

func TestDirKeyStore_CleanTmpDirMap(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Calculate current and old L1 values
	now := time.Now()
	currentOnD := gohelper.TimeToDateInt(now)
	currentL1 := CalculateL1(currentOnD, "TRB")

	// Calculate old date (DEFAULT_L1_DIFF_LIMIT + 1 weeks ago)
	oldDate := now.AddDate(0, 0, -(DEFAULT_L1_DIFF_LIMIT+1)*7)
	oldOnD := gohelper.TimeToDateInt(oldDate)
	oldL1 := CalculateL1(oldOnD, "TRB")

	// Add test data
	store.AddTmpDirMap(currentL1, []string{"abc12"})
	store.AddTmpDirMap(oldL1, []string{"def34"})

	// Clean old entries
	store.CleanTmpDirMap()

	// Check if old entry was removed
	_, ok := store.tmpDirMap.Load(oldL1)
	assert.False(t, ok)

	// Check if current entry remains
	value, ok := store.tmpDirMap.Load(currentL1)
	assert.True(t, ok)
	assert.NotNil(t, value)
}

func TestDirKeyStore_CleanTmpDirMap_StatsBasedBoard(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// Temporarily add a stats-based board to the configuration
	originalL2Size := SOURCE_L2_SIZE["STATSBOARD"]
	SOURCE_L2_SIZE["STATSBOARD"] = 64
	defer func() {
		if originalL2Size == 0 {
			delete(SOURCE_L2_SIZE, "STATSBOARD")
		} else {
			SOURCE_L2_SIZE["STATSBOARD"] = originalL2Size
		}
	}()

	// Temporarily add image directory configuration for STATSBOARD
	originalImageStore := goconfig.Config("imageStore")
	if imgStore, ok := originalImageStore.(map[string]interface{}); ok {
		imgStore["reso_statsboard_image_dir"] = []interface{}{"/tmp/stats_images"}
		defer func() {
			delete(imgStore, "reso_statsboard_image_dir")
		}()
	}

	// Add STATSBOARD to BoardImageDirName temporarily
	originalBoardImageDir := BoardImageDirName["STATSBOARD"]
	BoardImageDirName["STATSBOARD"] = "reso_statsboard_image_dir"
	defer func() {
		if originalBoardImageDir == "" {
			delete(BoardImageDirName, "STATSBOARD")
		} else {
			BoardImageDirName["STATSBOARD"] = originalBoardImageDir
		}
	}()

	// Register a mock store for stats-based board
	mockStore := &DirKeyStore{
		prefix: "STATSBOARD",
		curL1:  "105", // Current L1 is 105
	}
	RegisterDirKeyStore("STATSBOARD", mockStore)
	defer UnregisterDirKeyStore("STATSBOARD")

	store, err := newTestDirKeyStore("STATSBOARD", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Set current L1 to 105
	store.curL1 = "105"

	// Add test data with different L1 values
	store.AddTmpDirMap("105", []string{"current"})  // Current L1
	store.AddTmpDirMap("104", []string{"recent"})   // Recent L1 (should keep)
	store.AddTmpDirMap("101", []string{"old"})      // Old L1 (should keep, within limit)
	store.AddTmpDirMap("100", []string{"very_old"}) // Very old L1 (should clean, beyond limit)

	// Clean old entries
	store.CleanTmpDirMap()

	// Verify current L1 still exists
	_, ok := store.tmpDirMap.Load("105")
	assert.True(t, ok, "Current L1 105 should not be cleaned")

	// Verify recent L1 still exists
	_, ok = store.tmpDirMap.Load("104")
	assert.True(t, ok, "Recent L1 104 should not be cleaned")

	// Verify L1 within limit still exists
	_, ok = store.tmpDirMap.Load("101")
	assert.True(t, ok, "L1 101 should not be cleaned (within limit)")

	// Verify very old L1 was cleaned (105 - 100 = 5 > DEFAULT_L1_DIFF_LIMIT=4)
	_, ok = store.tmpDirMap.Load("100")
	assert.False(t, ok, "Very old L1 100 should be cleaned (beyond limit)")
}

func TestDirKeyStore_FetchDirArray(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// Insert test data
	_, err = coll.UpdateOne(ctx,
		bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "board", Value: "TRB"},
				{Key: "l1", Value: "1200"},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "dirArray", Value: []string{"abc12", "def34"}},
			}},
		},
		options.Update().SetUpsert(true))
	assert.NoError(t, err)

	tests := []struct {
		name     string
		board    string
		propTsD  int
		wantErr  bool
		wantSize int
	}{
		{
			name:     "fetch from database",
			board:    "TRB",
			propTsD:  20240101,
			wantErr:  false,
			wantSize: 2,
		},
		{
			name:     "fetch from cache",
			board:    "TRB",
			propTsD:  20240101,
			wantErr:  false,
			wantSize: 2,
		},
		{
			name:     "invalid board",
			board:    "INVALID",
			propTsD:  20240101,
			wantErr:  true,
			wantSize: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirArray, err := store.FetchDirArray(ctx, tt.board, tt.propTsD)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, dirArray)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantSize, len(dirArray))
			}
		})
	}
}

func TestDirKeyStore_SaveDirArrayInDB(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// Insert test data first
	_, err = coll.UpdateOne(ctx,
		bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "board", Value: "TRB"},
				{Key: "l1", Value: "1200"},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "dirArray", Value: []string{"abc12", "def34"}},
			}},
		},
		options.Update().SetUpsert(true))
	assert.NoError(t, err)

	tests := []struct {
		name        string
		board       string
		propTsD     int
		force       bool
		wantErr     bool
		wantNewSize int
	}{
		{
			name:        "save new array",
			board:       "TRB",
			propTsD:     20240101,
			force:       false,
			wantErr:     false,
			wantNewSize: 2,
		},
		{
			name:        "force update existing array",
			board:       "TRB",
			propTsD:     20240101,
			force:       true,
			wantErr:     false,
			wantNewSize: 512,
		},
		{
			name:        "invalid board",
			board:       "INVALID",
			propTsD:     20240101,
			force:       false,
			wantErr:     true,
			wantNewSize: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirArray, err := store.SaveDirArrayInDB(ctx, tt.board, tt.propTsD, tt.force)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, dirArray)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantNewSize, len(dirArray))
			}
		})
	}
}

// Helper to create L1 directories for all tempDirs and L1s
func createL1Dirs(t *testing.T, tempDirs []string, l1s []string) {
	for _, dir := range tempDirs {
		for _, l1 := range l1s {
			l1Dir := filepath.Join(dir, l1)
			err := os.MkdirAll(l1Dir, 0755)
			require.NoError(t, err)
		}
	}
}

func mockGetImageDir(board string, tempDirs []string) ([]string, error) {
	if board == "TRB" {
		return tempDirs, nil
	}
	return originalGetImageDir(board)
}

// setupTestEnvironment creates test directories and store for saveStatsToFile tests
func setupTestEnvironment(t *testing.T) ([]string, *DirKeyStore, func()) {
	// Create temporary directories for testing
	tempDirs := make([]string, 3)
	for i := range tempDirs {
		dir, err := os.MkdirTemp("", fmt.Sprintf("gofile_test_%d_*", i))
		require.NoError(t, err)
		tempDirs[i] = dir
	}

	// Create test store with proper initialization
	store := &DirKeyStore{
		prefix:     "TRB",
		sourceRoot: tempDirs[0],
		dirMap:     make(map[string]DirStats),
	}

	// Mock GetImageDir to return our test directories
	originalGetImageDir := GetImageDir
	GetImageDir = func(board string) ([]string, error) { return mockGetImageDir(board, tempDirs) }

	cleanup := func() {
		// Restore GetImageDir
		GetImageDir = originalGetImageDir

		// Restore permissions for all subdirectories
		for _, dir := range tempDirs {
			err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
				if err != nil {
					return err
				}
				if info.IsDir() {
					if err := os.Chmod(path, 0755); err != nil {
						t.Logf("Failed to restore permissions for %s: %v", path, err)
					}
				}
				return nil
			})
			if err != nil {
				t.Errorf("Failed to restore permissions for %s: %v", dir, err)
			}
			// Now try to remove the directory
			if err := os.RemoveAll(dir); err != nil {
				t.Errorf("Failed to remove temporary directory: %v", err)
			}
		}
	}

	return tempDirs, store, cleanup
}

func TestSaveCurrentInFileWithData_NewFiles(t *testing.T) {
	tempDirs, store, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// Setup: Create L1 directories
	createL1Dirs(t, tempDirs, []string{"1200"})

	// Test data
	l1Map := map[string]map[string]DirStats{
		"1200": {
			"abc12": {EntityAmount: 5, FileAmount: 3},
			"def34": {EntityAmount: 2, FileAmount: 1},
		},
	}

	// Execute
	err := store.saveStatsToFile(l1Map)
	require.NoError(t, err)

	// Verify files exist in all directories
	for _, dir := range tempDirs {
		metaPath := filepath.Join(dir, "1200", "dir_stats.json")
		data, err := os.ReadFile(metaPath)
		require.NoError(t, err)

		var meta DirMetaInfo
		err = json.Unmarshal(data, &meta)
		require.NoError(t, err)

		// Verify stats
		assert.Equal(t, 7, meta.TotalEntities)
		assert.Equal(t, 4, meta.TotalFiles)
		assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, meta.L2Stats["abc12"])
		assert.Equal(t, DirStats{EntityAmount: 2, FileAmount: 1}, meta.L2Stats["def34"])
	}
}

func TestSaveCurrentInFileWithData_AccumulateStats(t *testing.T) {
	tempDirs, store, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// Setup: Create L1 directories and existing files
	createL1Dirs(t, tempDirs, []string{"1200"})

	// Create initial files with existing stats in all directories
	for _, dir := range tempDirs {
		l1Dir := filepath.Join(dir, "1200")
		existingMeta := DirMetaInfo{
			TotalEntities: 7,
			TotalFiles:    4,
			L2Stats: map[string]DirStats{
				"abc12": {EntityAmount: 5, FileAmount: 3},
				"def34": {EntityAmount: 2, FileAmount: 1},
			},
			LastUpdated: time.Now(),
		}
		data, err := json.MarshalIndent(existingMeta, "", "  ")
		require.NoError(t, err)
		err = os.WriteFile(filepath.Join(l1Dir, "dir_stats.json"), data, 0644)
		require.NoError(t, err)
	}

	// Test data - new stats to accumulate
	l1Map := map[string]map[string]DirStats{
		"1200": {
			"abc12": {EntityAmount: 3, FileAmount: 2},
			"def34": {EntityAmount: 1, FileAmount: 1},
		},
	}

	// Execute
	err := store.saveStatsToFile(l1Map)
	require.NoError(t, err)

	// Verify accumulated stats in all directories
	for _, dir := range tempDirs {
		metaPath := filepath.Join(dir, "1200", "dir_stats.json")
		data, err := os.ReadFile(metaPath)
		require.NoError(t, err)

		var meta DirMetaInfo
		err = json.Unmarshal(data, &meta)
		require.NoError(t, err)

		// Verify accumulated stats
		assert.Equal(t, 11, meta.TotalEntities)                                          // 5+3 + 2+1 = 11
		assert.Equal(t, 7, meta.TotalFiles)                                              // 3+2 + 1+1 = 7
		assert.Equal(t, DirStats{EntityAmount: 8, FileAmount: 5}, meta.L2Stats["abc12"]) // 5+3, 3+2
		assert.Equal(t, DirStats{EntityAmount: 3, FileAmount: 2}, meta.L2Stats["def34"]) // 2+1, 1+1
	}
}

func TestSaveCurrentInFileWithData_PermissionError(t *testing.T) {
	tempDirs, store, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// Setup: Create L1 directories
	createL1Dirs(t, tempDirs, []string{"1200"})

	// Make the second directory read-only
	err := os.Chmod(tempDirs[1], 0444)
	require.NoError(t, err)
	defer func() {
		// Restore directory permissions
		if err := os.Chmod(tempDirs[1], 0755); err != nil {
			t.Logf("Failed to restore directory permissions: %v", err)
		}
	}()

	// Verify we can't write to the directory
	testFile := filepath.Join(tempDirs[1], "test.txt")
	err = os.WriteFile(testFile, []byte("test"), 0644)
	require.Error(t, err) // This should fail

	// Test data
	l1Map := map[string]map[string]DirStats{
		"1200": {
			"abc12": {EntityAmount: 5, FileAmount: 3},
		},
	}

	// Execute - should fail due to permission error
	err = store.saveStatsToFile(l1Map)
	assert.Error(t, err)
}

func TestSaveCurrentInFileWithData_MultipleL1Dirs(t *testing.T) {
	tempDirs, store, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// Clean up any existing files
	for _, dir := range tempDirs {
		for _, l1 := range []string{"1200", "1201"} {
			metaPath := filepath.Join(dir, l1, "dir_stats.json")
			if _, err := os.Stat(metaPath); err == nil {
				if err := os.Remove(metaPath); err != nil {
					t.Errorf("Failed to remove meta file %s: %v", metaPath, err)
				}
			}
		}
	}

	// Create L1 directories
	createL1Dirs(t, tempDirs, []string{"1200", "1201"})

	// Add a dummy file to each L1 directory to ensure calculateDirSize works
	for _, dir := range tempDirs {
		for _, l1 := range []string{"1200", "1201"} {
			dummyFile := filepath.Join(dir, l1, "dummy.txt")
			err := os.WriteFile(dummyFile, []byte("dummy"), 0644)
			require.NoError(t, err)
		}
	}

	// Reset store state
	store.dirMap = make(map[string]DirStats)

	// Test data for multiple L1 directories
	l1Map := map[string]map[string]DirStats{
		"1200": {
			"abc12": {EntityAmount: 5, FileAmount: 3},
		},
		"1201": {
			"def34": {EntityAmount: 2, FileAmount: 1},
		},
	}

	// Add test data to store
	for l1, l2Stats := range l1Map {
		for l2, stats := range l2Stats {
			store.AddDirStats(l1, l2, stats.EntityAmount, stats.FileAmount)
		}
	}

	// Execute
	err := store.saveStatsToFile(l1Map)
	require.NoError(t, err)

	// Verify each L1 directory
	for _, dir := range tempDirs {
		// Check 1200
		metaPath := filepath.Join(dir, "1200", "dir_stats.json")
		data, err := os.ReadFile(metaPath)
		require.NoError(t, err, "Failed to read meta file for 1200")

		var meta DirMetaInfo
		err = json.Unmarshal(data, &meta)
		require.NoError(t, err)

		assert.Equal(t, 5, meta.TotalEntities, "TotalEntities mismatch for 1200")
		assert.Equal(t, 3, meta.TotalFiles, "TotalFiles mismatch for 1200")
		assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, meta.L2Stats["abc12"], "L2Stats mismatch for 1200")

		// Check 1201
		metaPath = filepath.Join(dir, "1201", "dir_stats.json")
		data, err = os.ReadFile(metaPath)
		require.NoError(t, err, "Failed to read meta file for 1201")

		err = json.Unmarshal(data, &meta)
		require.NoError(t, err)

		assert.Equal(t, 2, meta.TotalEntities, "TotalEntities mismatch for 1201")
		assert.Equal(t, 1, meta.TotalFiles, "TotalFiles mismatch for 1201")
		assert.Equal(t, DirStats{EntityAmount: 2, FileAmount: 1}, meta.L2Stats["def34"], "L2Stats mismatch for 1201")
	}
}

func TestSaveCurrentInFileWithData_NonExistentDir(t *testing.T) {
	tempDirs, store, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// Remove the third directory
	err := os.RemoveAll(tempDirs[2])
	require.NoError(t, err)

	// Mock GetImageDir to return only the deleted directory
	originalGetImageDir := GetImageDir
	GetImageDir = func(board string) ([]string, error) { return []string{tempDirs[2]}, nil }
	defer func() { GetImageDir = originalGetImageDir }()

	// Test data
	l1Map := map[string]map[string]DirStats{
		"1200": {
			"abc12": {EntityAmount: 5, FileAmount: 3},
		},
	}

	// Execute - should succeed as directories are created automatically
	err = store.saveStatsToFile(l1Map)
	require.NoError(t, err)

	// Verify that the directory and file were created successfully
	metaPath := filepath.Join(tempDirs[2], "1200", "dir_stats.json")
	assert.FileExists(t, metaPath, "Meta file should be created even if parent directory didn't exist")

	data, err := os.ReadFile(metaPath)
	require.NoError(t, err)

	var meta DirMetaInfo
	err = json.Unmarshal(data, &meta)
	require.NoError(t, err)

	// Verify stats
	assert.Equal(t, 5, meta.TotalEntities)
	assert.Equal(t, 3, meta.TotalFiles)
	assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, meta.L2Stats["abc12"])
}

// Add more test cases to improve coverage
func TestDirKeyStore_CalculateDirSize(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "gofile_test_*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Errorf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Create test files with different sizes
	files := []struct {
		name string
		size int64
	}{
		{"file1.txt", 100},
		{"file2.txt", 200},
		{"file3.txt", 300},
	}

	for _, f := range files {
		filePath := filepath.Join(tempDir, f.name)
		err := os.WriteFile(filePath, make([]byte, f.size), 0644)
		require.NoError(t, err)
	}

	// Test calculateDirSize
	actualSize, diskSize, err := calculateDirSize(tempDir)
	require.NoError(t, err)
	assert.Equal(t, int64(600), actualSize)        // 100 + 200 + 300
	assert.GreaterOrEqual(t, diskSize, actualSize) // Disk size should be >= actual size
}

func TestDirKeyStore_GroupStatsByL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add test data
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)
	store.AddDirStats("1201", "ghi56", 3, 2)

	// Test groupStatsByL1
	l1Map := groupStatsByL1(store.dirMap)

	// Verify results
	assert.Equal(t, 2, len(l1Map))
	assert.Equal(t, 2, len(l1Map["1200"]))
	assert.Equal(t, 1, len(l1Map["1201"]))

	// Check specific values
	assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, l1Map["1200"]["abc12"])
	assert.Equal(t, DirStats{EntityAmount: 2, FileAmount: 1}, l1Map["1200"]["def34"])
	assert.Equal(t, DirStats{EntityAmount: 3, FileAmount: 2}, l1Map["1201"]["ghi56"])
}

func TestDirKeyStore_InvalidBoard(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Test invalid board
	assert.False(t, IsValidBoard(""))
	assert.False(t, IsValidBoard("INVALID"))
	assert.True(t, IsValidBoard("TRB"))
}

func TestDirKeyStore_Close(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add some stats
	store.AddDirStats("1200", "abc12", 5, 3)

	// Close the store
	store.Close()

	// Verify that dirMap is NOT empty after Close()
	assert.Empty(t, store.dirMap, "Close() should also clear dirMap")
}

func TestDirKeyStore_AddDirStats_EdgeCases(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Test adding zero stats
	store.AddDirStats("1200", "abc12", 0, 0)
	key := "1200-abc12"
	_, exists := store.dirMap[key]
	assert.False(t, exists)

	// Test adding negative stats
	store.AddDirStats("1200", "abc12", -5, -3)
	stats, exists2 := store.dirMap[key]
	assert.True(t, exists2)
	assert.Equal(t, -5, stats.EntityAmount)
	assert.Equal(t, -3, stats.FileAmount)
}

func TestDirKeyStore_SaveChangedCurrent_NoChanges(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add some stats
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)

	// Save changes first time
	store.SaveChangedCurrent()

	// Try to save again without changes
	store.SaveChangedCurrent()

	// Verify that dirMap is still empty
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should remain empty when no changes are made")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithMultipleL1s(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add stats for multiple L1 directories
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1201", "def34", 2, 1)
	store.AddDirStats("1202", "ghi56", 3, 2)

	// Save changes
	store.SaveChangedCurrent()

	// Verify that dirMap is cleared
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should be cleared after saving changes")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithNegativeStats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add positive stats first
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)

	// Save changes
	store.SaveChangedCurrent()

	// Add negative stats
	store.AddDirStats("1200", "abc12", -2, -1)
	store.AddDirStats("1200", "def34", -1, -1)

	// Save changes again
	store.SaveChangedCurrent()

	// Verify that dirMap is cleared
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should be cleared after saving changes")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithZeroStats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Add some stats
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)

	// Save changes
	store.SaveChangedCurrent()

	// Add zero stats
	store.AddDirStats("1200", "abc12", 0, 0)
	store.AddDirStats("1200", "def34", 0, 0)

	// Save changes again
	store.SaveChangedCurrent()

	// Verify that dirMap is cleared
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should be cleared after saving changes")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithNilStore(t *testing.T) {
	var store *DirKeyStore
	assert.NotPanics(t, func() {
		store.SaveChangedCurrent()
	}, "SaveChangedCurrent should not panic when store is nil")
}

func TestDirKeyStore_SaveChangedCurrent_WithNilCollection(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	store.backend = nil // Set backend to nil after creation
	assert.NotPanics(t, func() {
		store.SaveChangedCurrent()
	}, "SaveChangedCurrent should not panic when backend is nil")
}

func TestDirKeyStore_SaveChangedCurrent_WithFileError(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "gofile_test_*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Errorf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Make directory read-only
	err = os.Chmod(tempDir, 0444)
	require.NoError(t, err)
	defer func() {
		if err := os.Chmod(tempDir, 0755); err != nil {
			t.Errorf("Failed to restore directory permissions: %v", err)
		}
	}()

	// Create test store with proper initialization
	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	store.sourceRoot = tempDir
	store.lastUpdateTs = time.Now()
	store.lastSavedTs = time.Now().Add(-time.Hour) // Make sure there are changes

	// Mock GetImageDir to return our test directory
	originalGetImageDir := GetImageDir
	GetImageDir = func(board string) ([]string, error) { return []string{tempDir}, nil }
	defer func() { GetImageDir = originalGetImageDir }()

	// Add test data
	store.AddDirStats("1200", "abc12", 5, 3)

	// Save changes - should not panic
	store.SaveChangedCurrent()

	// Verify database was still updated despite file error
	ctx := context.Background()
	var result struct {
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}

	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "1200"},
		}},
	}).Decode(&result)
	require.NoError(t, err)
	assert.Equal(t, 3, result.TotalFiles)
	assert.Equal(t, 5, result.TotalEntities)
	assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, result.L2Stats["abc12"])
}

func TestDirKeyStore_SaveChangedCurrent_WithDBError(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	store.backend = nil // Set backend to nil after creation
	store.lastUpdateTs = time.Now()
	store.lastSavedTs = time.Now().Add(-time.Hour) // Make sure there are changes

	// Add test data
	store.AddDirStats("1200", "abc12", 5, 3)

	// Save changes - should not panic
	assert.NotPanics(t, func() {
		store.SaveChangedCurrent()
	}, "SaveChangedCurrent should not panic when collection is nil")
}

func TestDirKeyStore_SetSourceRoot(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name     string
		root     string
		expected string
	}{
		{
			name:     "set valid root",
			root:     "/custom/path",
			expected: "/custom/path",
		},
		{
			name:     "set empty root",
			root:     "",
			expected: DEFAULT_SOURCE_ROOT, // Should keep default
		},
		{
			name:     "set root with spaces",
			root:     "  /path/with/spaces  ",
			expected: "/path/with/spaces",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.SetSourceRoot(tt.root)
			assert.Equal(t, tt.expected, store.sourceRoot)
		})
	}
}

// Test the new extracted DirKeyStore methods
func TestDirKeyStore_FetchDirArrayByL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	board := "TRB"
	l1 := "1200"

	tests := []struct {
		name      string
		board     string
		l1        string
		setupData bool
		wantErr   bool
	}{
		{
			name:      "valid fetch with existing data",
			board:     board,
			l1:        l1,
			setupData: true,
			wantErr:   false,
		},
		{
			name:      "fetch non-existent data creates new",
			board:     board,
			l1:        "9999",
			setupData: false,
			wantErr:   false, // Should create new array
		},
		{
			name:      "invalid board",
			board:     "INVALID",
			l1:        l1,
			setupData: false,
			wantErr:   true,
		},
		{
			name:      "empty l1",
			board:     board,
			l1:        "",
			setupData: false,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test data if needed
			if tt.setupData {
				_, err := store.SaveDirArrayByL1(ctx, board, l1, true)
				require.NoError(t, err)
			}

			result, err := store.FetchDirArrayByL1(ctx, tt.board, tt.l1)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)
			}
		})
	}
}

func TestDirKeyStore_SaveDirArrayByL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	board := "TRB"
	l1 := "1201"

	tests := []struct {
		name        string
		board       string
		l1          string
		forceUpdate bool
		wantErr     bool
	}{
		{
			name:        "create new array",
			board:       board,
			l1:          l1,
			forceUpdate: false,
			wantErr:     false,
		},
		{
			name:        "force update existing array",
			board:       board,
			l1:          l1,
			forceUpdate: true,
			wantErr:     false,
		},
		{
			name:        "create array for new l1",
			board:       board,
			l1:          "1202",
			forceUpdate: false,
			wantErr:     false,
		},
		{
			name:        "invalid board",
			board:       "INVALID",
			l1:          l1,
			forceUpdate: false,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := store.SaveDirArrayByL1(ctx, tt.board, tt.l1, tt.forceUpdate)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)
			}
		})
	}
}

func TestDirKeyStore_FetchSave_Integration(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	board := "TRB"
	l1 := "1203"

	// Test integration: save then fetch
	t.Run("save_then_fetch", func(t *testing.T) {
		// First save an array
		savedArray, err := store.SaveDirArrayByL1(ctx, board, l1, true)
		require.NoError(t, err)
		require.NotNil(t, savedArray)
		require.Greater(t, len(savedArray), 0)

		// Then fetch the same array
		fetchedArray, err := store.FetchDirArrayByL1(ctx, board, l1)
		require.NoError(t, err)
		require.NotNil(t, fetchedArray)

		// Should be the same
		assert.Equal(t, savedArray, fetchedArray)
	})

	// Test fetch non-existent (should auto-create)
	t.Run("fetch_nonexistent_autocreate", func(t *testing.T) {
		l1New := "1204"

		// Fetch non-existent should auto-create
		fetchedArray, err := store.FetchDirArrayByL1(ctx, board, l1New)
		require.NoError(t, err)
		require.NotNil(t, fetchedArray)
		require.Greater(t, len(fetchedArray), 0)

		// Fetch again should return the same array
		fetchedArray2, err := store.FetchDirArrayByL1(ctx, board, l1New)
		require.NoError(t, err)
		assert.Equal(t, fetchedArray, fetchedArray2)
	})

	// Test original methods still work
	t.Run("original_methods_compatibility", func(t *testing.T) {
		propTsD := 20240615 // 2024-06-15

		// Original FetchDirArray should work
		array1, err := store.FetchDirArray(ctx, board, propTsD)
		require.NoError(t, err)
		require.NotNil(t, array1)

		// Original SaveDirArrayInDB should work
		array2, err := store.SaveDirArrayInDB(ctx, board, propTsD, true)
		require.NoError(t, err)
		require.NotNil(t, array2)

		// Should be the same
		assert.Equal(t, array1, array2)
	})
}

func TestDirKeyStore_SaveStatsToFile_Refactored(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	tests := []struct {
		name        string
		l1Map       map[string]map[string]DirStats
		expectError bool
		description string
	}{
		{
			name: "Valid stats with single L1",
			l1Map: map[string]map[string]DirStats{
				"abc12": {
					"def34": {EntityAmount: 10, FileAmount: 5},
					"ghi56": {EntityAmount: 20, FileAmount: 10},
				},
			},
			expectError: false,
			description: "Should successfully save stats for single L1",
		},
		{
			name: "Valid stats with multiple L1s",
			l1Map: map[string]map[string]DirStats{
				"abc12": {
					"def34": {EntityAmount: 10, FileAmount: 5},
				},
				"xyz78": {
					"uvw90": {EntityAmount: 15, FileAmount: 8},
				},
			},
			expectError: false,
			description: "Should successfully save stats for multiple L1s",
		},
		{
			name:        "Empty L1 map",
			l1Map:       map[string]map[string]DirStats{},
			expectError: false,
			description: "Should handle empty L1 map without error",
		},
		{
			name: "L1 with empty L2 stats",
			l1Map: map[string]map[string]DirStats{
				"abc12": {},
			},
			expectError: false,
			description: "Should handle L1 with empty L2 stats",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, err := newTestDirKeyStore("TRB", coll, "")
			assert.NoError(t, err, "Failed to create DirKeyStore")
			defer store.Close()

			err = store.saveStatsToFile(tt.l1Map)

			if tt.expectError {
				assert.Error(t, err, "Expected error for: %s", tt.description)
			} else {
				assert.NoError(t, err, "Unexpected error for: %s", tt.description)

				// Verify files were created
				dirs, err := GetImageDir("TRB")
				require.NoError(t, err)
				for l1 := range tt.l1Map {
					for _, dir := range dirs {
						metaPath := filepath.Join(dir, l1, "dir_stats.json")
						assert.FileExists(t, metaPath, "Meta file should exist for L1: %s", l1)

						// Verify file content
						data, err := os.ReadFile(metaPath)
						assert.NoError(t, err, "Should be able to read meta file")

						var metaInfo DirMetaInfo
						err = json.Unmarshal(data, &metaInfo)
						assert.NoError(t, err, "Should be able to parse meta file")

						// Verify human-readable sizes are present
						assert.NotEmpty(t, metaInfo.ActualSizeHuman, "ActualSizeHuman should not be empty")
						assert.NotEmpty(t, metaInfo.DiskSizeHuman, "DiskSizeHuman should not be empty")
					}
				}
			}
		})
	}
}

func TestDirKeyStore_CalculateL1Stats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	assert.NoError(t, err, "Failed to create DirKeyStore")
	defer store.Close()

	l1Map := map[string]map[string]DirStats{
		"abc12": {
			"def34": {EntityAmount: 10, FileAmount: 5},
			"ghi56": {EntityAmount: 20, FileAmount: 10},
		},
		"xyz78": {
			"uvw90": {EntityAmount: 15, FileAmount: 8},
		},
	}

	dirs, err := GetImageDir("TRB")
	require.NoError(t, err)
	require.NotEmpty(t, dirs, "Should have configured directories")

	result := store.calculateL1Stats(l1Map, dirs[0])

	assert.Len(t, result, 2, "Should have stats for 2 L1 directories")

	// Check abc12 stats
	abc12Stats, exists := result["abc12"]
	assert.True(t, exists, "Should have stats for abc12")
	assert.NoError(t, abc12Stats.lastError, "Should not have error for abc12")
	// Note: These values might be higher due to merging with existing data from previous tests
	assert.GreaterOrEqual(t, abc12Stats.metaInfo.TotalEntities, 30, "Total entities should be at least 30")
	assert.GreaterOrEqual(t, abc12Stats.metaInfo.TotalFiles, 15, "Total files should be at least 15")
	assert.GreaterOrEqual(t, len(abc12Stats.metaInfo.L2Stats), 2, "Should have at least 2 L2 stats")

	// Check xyz78 stats
	xyz78Stats, exists := result["xyz78"]
	assert.True(t, exists, "Should have stats for xyz78")
	assert.NoError(t, xyz78Stats.lastError, "Should not have error for xyz78")
	// Note: These values might be higher due to merging with existing data from previous tests
	assert.GreaterOrEqual(t, xyz78Stats.metaInfo.TotalEntities, 15, "Total entities should be at least 15")
	assert.GreaterOrEqual(t, xyz78Stats.metaInfo.TotalFiles, 8, "Total files should be at least 8")
	assert.GreaterOrEqual(t, len(xyz78Stats.metaInfo.L2Stats), 1, "Should have at least 1 L2 stat")
}

func TestDirKeyStore_LoadExistingMeta(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	assert.NoError(t, err, "Failed to create DirKeyStore")
	defer store.Close()

	// Create a temporary directory and meta file
	tempDir := t.TempDir()
	metaPath := filepath.Join(tempDir, "dir_stats.json")

	tests := []struct {
		name        string
		setupFile   bool
		fileContent string
		l2Stats     map[string]DirStats
		expectError bool
	}{
		{
			name:      "File doesn't exist",
			setupFile: false,
			l2Stats: map[string]DirStats{
				"test": {EntityAmount: 5, FileAmount: 3},
			},
			expectError: false,
		},
		{
			name:      "Valid existing file",
			setupFile: true,
			fileContent: `{
				"total_entities": 10,
				"total_files": 5,
				"l2_stats": {
					"existing": {"entity_amount": 5, "file_amount": 3}
				},
				"actual_size": 1024,
				"disk_size": 2048,
				"actual_size_human": "1.0 KB",
				"disk_size_human": "2.0 KB",
				"last_updated": "2023-01-01T00:00:00Z"
			}`,
			l2Stats: map[string]DirStats{
				"test": {EntityAmount: 5, FileAmount: 3},
			},
			expectError: false,
		},
		{
			name:        "Corrupted JSON file",
			setupFile:   true,
			fileContent: `{invalid json`,
			l2Stats: map[string]DirStats{
				"test": {EntityAmount: 5, FileAmount: 3},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup file if needed
			if tt.setupFile {
				err := os.WriteFile(metaPath, []byte(tt.fileContent), 0644)
				assert.NoError(t, err, "Failed to create test file")
				defer func() {
					// Try to remove the original file
					if err := os.Remove(metaPath); err != nil && !os.IsNotExist(err) {
						t.Errorf("Failed to remove test file: %v", err)
					}

					// For corrupted file test, also clean up the backup file
					if tt.name == "Corrupted JSON file" {
						// Find and remove backup files (they have timestamp suffix)
						dir := filepath.Dir(metaPath)
						files, err := filepath.Glob(filepath.Join(dir, "dir_stats.json.corrupted.*"))
						if err == nil {
							for _, file := range files {
								if err := os.Remove(file); err != nil && !os.IsNotExist(err) {
									t.Errorf("Failed to remove backup file: %v", err)
								}
							}
						}
					}
				}()
			}

			result, err := store.loadExistingMeta(metaPath, tt.l2Stats)

			if tt.expectError {
				assert.Error(t, err, "Expected error for: %s", tt.name)

				// For corrupted file test, verify that backup file was created
				if tt.name == "Corrupted JSON file" {
					dir := filepath.Dir(metaPath)
					files, globErr := filepath.Glob(filepath.Join(dir, "dir_stats.json.corrupted.*"))
					assert.NoError(t, globErr, "Should be able to glob for backup files")
					assert.NotEmpty(t, files, "Should have created backup file for corrupted JSON")
				}
			} else {
				assert.NoError(t, err, "Unexpected error for: %s", tt.name)
				assert.NotNil(t, result.L2Stats, "L2Stats should be initialized")

				if tt.setupFile && !tt.expectError {
					// Should have loaded existing data
					assert.Contains(t, result.L2Stats, "existing", "Should contain existing L2 stat")
				}
			}
		})
	}
}

func TestDirKeyStore_MergeL2Stats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	assert.NoError(t, err, "Failed to create DirKeyStore")
	defer store.Close()

	existingMeta := DirMetaInfo{
		L2Stats: map[string]DirStats{
			"existing1": {EntityAmount: 10, FileAmount: 5},
			"existing2": {EntityAmount: 20, FileAmount: 10},
		},
	}

	newL2Stats := map[string]DirStats{
		"existing1": {EntityAmount: 5, FileAmount: 3},  // Should be added to existing
		"new1":      {EntityAmount: 15, FileAmount: 8}, // Should be new entry
	}

	result := store.mergeL2Stats(existingMeta, newL2Stats)

	// Check merged stats
	assert.Equal(t, 15, result.L2Stats["existing1"].EntityAmount, "Should merge existing1 entities")
	assert.Equal(t, 8, result.L2Stats["existing1"].FileAmount, "Should merge existing1 files")

	// Check unchanged existing stats
	assert.Equal(t, 20, result.L2Stats["existing2"].EntityAmount, "Should keep existing2 entities unchanged")
	assert.Equal(t, 10, result.L2Stats["existing2"].FileAmount, "Should keep existing2 files unchanged")

	// Check new stats
	assert.Equal(t, 15, result.L2Stats["new1"].EntityAmount, "Should add new1 entities")
	assert.Equal(t, 8, result.L2Stats["new1"].FileAmount, "Should add new1 files")
}

func TestFormatBytes(t *testing.T) {
	tests := []struct {
		name     string
		bytes    int64
		expected string
	}{
		// Basic cases
		{"Zero bytes", 0, "0 B"},
		{"Negative bytes", -1, "0 B"},
		{"Negative large bytes", -1024, "0 B"},

		// Bytes (< 1024)
		{"1 byte", 1, "1 B"},
		{"Small bytes", 512, "512 B"},
		{"Max bytes before KB", 1023, "1023 B"},

		// Kilobytes
		{"Exactly 1 KB", 1024, "1.0 KB"},
		{"1.5 KB", 1536, "1.5 KB"},
		{"2 KB", 2048, "2.0 KB"},
		{"10 KB", 10240, "10.0 KB"},
		{"100 KB", 102400, "100.0 KB"},
		{"Max KB before MB", 1048575, "1024.0 KB"},

		// Megabytes
		{"Exactly 1 MB", 1024 * 1024, "1.0 MB"},
		{"1.5 MB", 1536 * 1024, "1.5 MB"},
		{"10 MB", 10 * 1024 * 1024, "10.0 MB"},
		{"100 MB", 100 * 1024 * 1024, "100.0 MB"},
		{"Max MB before GB", 1073741823, "1024.0 MB"},

		// Gigabytes
		{"Exactly 1 GB", 1024 * 1024 * 1024, "1.0 GB"},
		{"1.5 GB", 1536 * 1024 * 1024, "1.5 GB"},
		{"10 GB", 10 * 1024 * 1024 * 1024, "10.0 GB"},
		{"100 GB", 100 * 1024 * 1024 * 1024, "100.0 GB"},

		// Terabytes
		{"Exactly 1 TB", 1024 * 1024 * 1024 * 1024, "1.0 TB"},
		{"1.5 TB", 1536 * 1024 * 1024 * 1024, "1.5 TB"},
		{"10 TB", 10 * 1024 * 1024 * 1024 * 1024, "10.0 TB"},

		// Petabytes
		{"Exactly 1 PB", 1024 * 1024 * 1024 * 1024 * 1024, "1.0 PB"},
		{"1.5 PB", 1536 * 1024 * 1024 * 1024 * 1024, "1.5 PB"},

		// Exabytes
		{"Exactly 1 EB", 1024 * 1024 * 1024 * 1024 * 1024 * 1024, "1.0 EB"},

		// Real-world examples from the original request
		{"Example actual_size", 195688, "191.1 KB"},
		{"Example disk_size", 200704, "196.0 KB"},

		// Edge cases with fractional results
		{"1.1 KB", 1126, "1.1 KB"},
		{"1.9 KB", 1946, "1.9 KB"},
		{"1.1 MB", 1153434, "1.1 MB"},
		{"1.9 MB", 1992294, "1.9 MB"},
		{"1.1 GB", 1181116006, "1.1 GB"},
		{"1.9 GB", 2040109465, "1.9 GB"},

		// Large numbers
		{"Very large number", 9223372036854775807, "8.0 EB"}, // max int64
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatBytes(tt.bytes)
			if result != tt.expected {
				t.Errorf("formatBytes(%d) = %s, want %s", tt.bytes, result, tt.expected)
			}
		})
	}
}

func TestFormatBytes_Precision(t *testing.T) {
	// Test precision and rounding behavior
	tests := []struct {
		name     string
		bytes    int64
		expected string
	}{
		// Test rounding behavior - Go uses "round half to even" (banker's rounding)
		{"1.04 KB (should round to 1.0)", 1065, "1.0 KB"},
		{"1.05 KB (should round to 1.0)", 1075, "1.0 KB"}, // 1.0498... rounds to 1.0
		{"1.94 KB (should round to 1.9)", 1987, "1.9 KB"},
		{"1.95 KB (should round to 2.0)", 1997, "2.0 KB"},

		// Test decimal precision
		{"1.11 KB", 1136, "1.1 KB"}, // Should show 1.1, not 1.11
		{"1.16 KB", 1188, "1.2 KB"}, // Should round to 1.2
		{"1.99 KB", 2038, "2.0 KB"}, // Should round to 2.0

		// Additional precision tests
		{"1.06 KB", 1085, "1.1 KB"}, // 1.0595... rounds to 1.1
		{"1.15 KB", 1178, "1.2 KB"}, // 1.1503... rounds to 1.2
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatBytes(tt.bytes)
			if result != tt.expected {
				t.Errorf("formatBytes(%d) = %s, want %s", tt.bytes, result, tt.expected)
			}
		})
	}
}

func TestFormatBytes_Consistency(t *testing.T) {
	// Test that the function is consistent across different ranges
	testRanges := []struct {
		name      string
		baseValue int64
		unit      string
	}{
		{"KB range", 1024, "KB"},
		{"MB range", 1024 * 1024, "MB"},
		{"GB range", 1024 * 1024 * 1024, "GB"},
		{"TB range", 1024 * 1024 * 1024 * 1024, "TB"},
	}

	for _, tr := range testRanges {
		t.Run(tr.name, func(t *testing.T) {
			// Test exact unit boundary
			result := formatBytes(tr.baseValue)
			expected := "1.0 " + tr.unit
			if result != expected {
				t.Errorf("formatBytes(%d) = %s, want %s", tr.baseValue, result, expected)
			}

			// Test half unit
			halfValue := tr.baseValue + tr.baseValue/2
			result = formatBytes(halfValue)
			expected = "1.5 " + tr.unit
			if result != expected {
				t.Errorf("formatBytes(%d) = %s, want %s", halfValue, result, expected)
			}
		})
	}
}

func BenchmarkFormatBytes(b *testing.B) {
	testValues := []int64{
		0, 512, 1024, 1536, 1024 * 1024, 1536 * 1024 * 1024, 1024 * 1024 * 1024 * 1024,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, val := range testValues {
			formatBytes(val)
		}
	}
}

// TestDirKeyStore_GetCurrentL1 tests the getCurrentL1 method
func TestDirKeyStore_GetCurrentL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name     string
		setL1    string
		expected string
	}{
		{
			name:     "default L1",
			setL1:    "100",
			expected: "100",
		},
		{
			name:     "upgraded L1",
			setL1:    "105",
			expected: "105",
		},
		{
			name:     "string L1",
			setL1:    "999",
			expected: "999",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.curL1 = tt.setL1
			result := store.getCurrentL1()
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDirKeyStore_LoadCurrentL1 tests loading L1 from backend
func TestDirKeyStore_LoadCurrentL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name            string
		setupL1         string
		expectedL1      string
		shouldSaveFirst bool
	}{
		{
			name:            "load existing L1",
			setupL1:         "150",
			expectedL1:      "150",
			shouldSaveFirst: true,
		},
		{
			name:            "load default when no record",
			setupL1:         "",
			expectedL1:      "100", // DEFAULT_L1_START_NUM
			shouldSaveFirst: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up any existing L1 record in MongoDB first
			filter := bson.D{
				{Key: "_id", Value: bson.D{
					{Key: "board", Value: store.boardKey},
					{Key: "type", Value: "current_l1"},
				}},
			}
			_, err := coll.DeleteOne(context.Background(), filter)
			require.NoError(t, err)

			if tt.shouldSaveFirst {
				// Setup: save L1 to backend first
				store.curL1 = tt.setupL1
				err := store.saveCurrentL1()
				require.NoError(t, err)
			}

			// Clear current L1 to simulate fresh load
			store.curL1 = ""

			// Test loading
			err = store.loadCurrentL1()
			require.NoError(t, err)

			assert.Equal(t, tt.expectedL1, store.getCurrentL1())
		})
	}
}

// TestDirKeyStore_SaveCurrentL1 tests saving L1 to backend
func TestDirKeyStore_SaveCurrentL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name string
		l1   string
	}{
		{
			name: "save initial L1",
			l1:   "100",
		},
		{
			name: "save upgraded L1",
			l1:   "101",
		},
		{
			name: "save high L1",
			l1:   "999",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.curL1 = tt.l1

			// Save to backend
			err := store.saveCurrentL1()
			require.NoError(t, err)

			// Verify by loading back
			store.curL1 = "" // Clear to test loading
			err = store.loadCurrentL1()
			require.NoError(t, err)

			assert.Equal(t, tt.l1, store.getCurrentL1())
		})
	}
}

// TestDirKeyStore_L1UpgradeLogic tests L1 upgrade logic through SaveChangedCurrent
// This replaces the old checkAndUpgradeL1 test since upgrade logic is now in saveStatsToDb
func TestDirKeyStore_L1UpgradeLogic(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name          string
		setupData     func() // Setup existing data in DB
		addStats      func() // Add incremental stats
		expectedL1    string
		shouldUpgrade bool
	}{
		{
			name: "no upgrade needed - low file count",
			setupData: func() {
				// No existing data
			},
			addStats: func() {
				store.AddDirStats("100", "abc12", 1000, 1000)
				store.AddDirStats("100", "def34", 2000, 2000)
			},
			expectedL1:    "100",
			shouldUpgrade: false,
		},
		{
			name: "upgrade needed - high incremental count",
			setupData: func() {
				// No existing data
			},
			addStats: func() {
				store.AddDirStats("100", "abc12", 500000, 500000)
				store.AddDirStats("100", "def34", 500000, 500001) // Total > 1M
			},
			expectedL1:    "101",
			shouldUpgrade: true,
		},
		{
			name: "upgrade needed - existing + incremental > limit",
			setupData: func() {
				// Add existing data to database (900k files)
				ctx := context.Background()
				filter := bson.D{
					{Key: "_id", Value: bson.D{
						{Key: "board", Value: "TRB"},
						{Key: "l1", Value: "100"},
					}},
				}
				update := bson.M{
					"$set": bson.M{
						"l2Stats": map[string]DirStats{
							"existing1": {EntityAmount: 450000, FileAmount: 450000},
							"existing2": {EntityAmount: 450000, FileAmount: 450000},
						},
						"totalFiles":    900000,
						"totalEntities": 900000,
					},
				}
				_, err := coll.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
				require.NoError(t, err)
			},
			addStats: func() {
				store.AddDirStats("100", "new1", 100000, 100000)
				store.AddDirStats("100", "new2", 100000, 100001) // Total will be 1.1M
			},
			expectedL1:    "101",
			shouldUpgrade: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up database
			_, err := coll.DeleteMany(context.Background(), bson.M{})
			require.NoError(t, err)

			// Reset L1 to 100 for each test
			store.curL1 = "100"
			err = store.saveCurrentL1()
			require.NoError(t, err)

			initialL1 := store.getCurrentL1()

			// Setup existing data if needed
			tt.setupData()

			// Add incremental stats
			tt.addStats()

			// Trigger save which should check for upgrade
			store.SaveChangedCurrent()

			finalL1 := store.getCurrentL1()
			assert.Equal(t, tt.expectedL1, finalL1)

			if tt.shouldUpgrade {
				assert.NotEqual(t, initialL1, finalL1, "L1 should have been upgraded")
			} else {
				assert.Equal(t, initialL1, finalL1, "L1 should not have been upgraded")
			}
		})
	}
}

// TestDirKeyStore_RegistrationLifecycle tests the complete registration lifecycle
func TestDirKeyStore_RegistrationLifecycle(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	boardName := "REGTEST"

	// Verify board is not registered initially
	assert.Equal(t, "100", getStatsBasedL1(boardName), "Should return fallback before registration")

	// Create and register store
	store, err := newTestDirKeyStore("TRB", coll, "") // Use TRB to create valid store
	require.NoError(t, err)

	// Set specific L1 value
	store.curL1 = "150"

	// Manually register for test board
	originalStore, existed := globalStores.LoadAndDelete(boardName)
	globalStores.Store(boardName, store)

	// Verify registration works
	assert.Equal(t, "150", getStatsBasedL1(boardName), "Should return registered L1")
	assert.Equal(t, "150", store.getCurrentL1(), "Store should return correct L1")

	// Test unregistration
	globalStores.Delete(boardName)
	assert.Equal(t, "100", getStatsBasedL1(boardName), "Should return fallback after unregistration")

	// Test Close() also unregisters
	globalStores.Store(boardName, store) // Re-register
	assert.Equal(t, "150", getStatsBasedL1(boardName), "Should return registered L1 again")

	store.Close() // This should unregister TRB, but not our test board

	// Clean up test registration
	globalStores.Delete(boardName)
	if existed {
		globalStores.Store(boardName, originalStore)
	}

	assert.Equal(t, "100", getStatsBasedL1(boardName), "Should return fallback after Close()")
}

// TestDirKeyStore_ConcurrentAccess tests thread-safe access to getCurrentL1
func TestDirKeyStore_ConcurrentAccess(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Set initial L1
	store.curL1 = "100"

	// Test concurrent reads
	const numGoroutines = 10
	const numReads = 100

	results := make(chan string, numGoroutines*numReads)

	// Start multiple goroutines reading getCurrentL1
	for i := 0; i < numGoroutines; i++ {
		go func() {
			for j := 0; j < numReads; j++ {
				result := store.getCurrentL1()
				results <- result
			}
		}()
	}

	// Collect all results
	for i := 0; i < numGoroutines*numReads; i++ {
		result := <-results
		assert.Equal(t, "100", result, "All concurrent reads should return consistent value")
	}
}

// TestDirKeyStore_GetCurrentL1_EdgeCases tests edge cases for getCurrentL1
func TestDirKeyStore_GetCurrentL1_EdgeCases(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name     string
		setupL1  string
		expected string
	}{
		{
			name:     "empty string L1",
			setupL1:  "",
			expected: "",
		},
		{
			name:     "whitespace L1",
			setupL1:  "   ",
			expected: "   ",
		},
		{
			name:     "very long L1",
			setupL1:  "123456789012345678901234567890",
			expected: "123456789012345678901234567890",
		},
		{
			name:     "special characters L1",
			setupL1:  "L1-test_123",
			expected: "L1-test_123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.curL1 = tt.setupL1
			result := store.getCurrentL1()
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDirKeyStore_ConcurrentReadWrite tests concurrent read/write access
func TestDirKeyStore_ConcurrentReadWrite(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Set initial L1
	store.curL1 = "100"

	const numReaders = 5
	const numWriters = 2
	const numOperations = 50
	const testDuration = 2 * time.Second

	var wg sync.WaitGroup
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()

	// Track all read results
	readResults := make(chan string, numReaders*numOperations)

	// Start reader goroutines
	for i := 0; i < numReaders; i++ {
		wg.Add(1)
		go func(readerID int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				select {
				case <-ctx.Done():
					return
				default:
					result := store.getCurrentL1()
					readResults <- result
					time.Sleep(time.Millisecond) // Small delay to allow interleaving
				}
			}
		}(i)
	}

	// Start writer goroutines (simulating L1 upgrades)
	for i := 0; i < numWriters; i++ {
		wg.Add(1)
		go func(writerID int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				select {
				case <-ctx.Done():
					return
				default:
					// Simulate L1 upgrade
					newL1 := fmt.Sprintf("%d", 100+j)
					store.curL1 = newL1
					time.Sleep(2 * time.Millisecond) // Slightly longer delay for writes
				}
			}
		}(i)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(readResults)

	// Verify all reads returned valid L1 values
	validL1Pattern := regexp.MustCompile(`^\d+$`)
	readCount := 0
	for result := range readResults {
		readCount++
		assert.True(t, validL1Pattern.MatchString(result),
			"Read result should be a valid L1 value, got: %s", result)

		// L1 should be in expected range (100-149 based on our test)
		l1Int, err := strconv.Atoi(result)
		assert.NoError(t, err, "L1 should be convertible to int")
		assert.True(t, l1Int >= 100 && l1Int < 150,
			"L1 should be in expected range, got: %d", l1Int)
	}

	assert.True(t, readCount > 0, "Should have collected some read results")
}

// TestDirKeyStore_GetCurrentL1_MongoDBIntegration tests getCurrentL1 with MongoDB operations
func TestDirKeyStore_GetCurrentL1_MongoDBIntegration(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name              string
		initialL1         string
		mongoL1           string
		expectedAfterLoad string
	}{
		{
			name:              "load from empty MongoDB",
			initialL1:         "999",
			mongoL1:           "",    // No record in MongoDB
			expectedAfterLoad: "100", // Should use default
		},
		{
			name:              "load from MongoDB overrides initial",
			initialL1:         "999",
			mongoL1:           "150",
			expectedAfterLoad: "150",
		},
		{
			name:              "load same value from MongoDB",
			initialL1:         "120",
			mongoL1:           "120",
			expectedAfterLoad: "120",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up any existing L1 record
			filter := bson.D{
				{Key: "_id", Value: bson.D{
					{Key: "board", Value: store.prefix},
					{Key: "type", Value: "current_l1"},
				}},
			}
			_, err := coll.DeleteOne(context.Background(), filter)
			require.NoError(t, err)

			// Set initial L1
			store.curL1 = tt.initialL1
			assert.Equal(t, tt.initialL1, store.getCurrentL1(), "Initial L1 should be set")

			// Save specific L1 to MongoDB if specified
			if tt.mongoL1 != "" {
				store.curL1 = tt.mongoL1
				err = store.saveCurrentL1()
				require.NoError(t, err)
			}

			// Clear current L1 and load from MongoDB
			store.curL1 = ""
			err = store.loadCurrentL1()
			require.NoError(t, err)

			// Verify the loaded L1
			result := store.getCurrentL1()
			assert.Equal(t, tt.expectedAfterLoad, result, "L1 should be loaded correctly from MongoDB")
		})
	}
}

// TestDirKeyStore_UpgradeL1_Integration tests the complete upgrade flow
func TestDirKeyStore_UpgradeL1_Integration(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Set initial L1
	initialL1 := "100"
	store.curL1 = initialL1
	err = store.saveCurrentL1()
	require.NoError(t, err)

	// Add stats that will trigger upgrade
	store.AddDirStats(initialL1, "abc12", 500000, 500000)
	store.AddDirStats(initialL1, "def34", 500000, 500001) // Total > 1M

	// Trigger save which should upgrade L1
	store.SaveChangedCurrent()

	// Verify L1 was upgraded
	upgradedL1 := store.getCurrentL1()
	assert.Equal(t, "101", upgradedL1, "L1 should be upgraded to 101")

	// Verify it was saved to MongoDB
	store.curL1 = "" // Clear memory
	err = store.loadCurrentL1()
	require.NoError(t, err)
	assert.Equal(t, "101", store.getCurrentL1(), "Upgraded L1 should be persisted in MongoDB")
}

// TestDirKeyStore_UpgradeL1_WithExistingData tests L1 upgrade with existing data in database
// This test verifies that L1 upgrade works correctly with existing data + incremental data
func TestDirKeyStore_UpgradeL1_WithExistingData(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := newTestDirKeyStore("TRB", coll, "")
	require.NoError(t, err)
	defer store.Close()

	// Set initial L1
	initialL1 := "100"
	store.curL1 = initialL1
	err = store.saveCurrentL1()
	require.NoError(t, err)

	// 1. First, save existing data to database (900k files)
	ctx := context.Background()
	filter := bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "100"},
		}},
	}
	update := bson.M{
		"$set": bson.M{
			"l2Stats": map[string]DirStats{
				"existing1": {EntityAmount: 450000, FileAmount: 450000},
				"existing2": {EntityAmount: 450000, FileAmount: 450000},
			},
			"totalFiles":    900000, // Already have 900k files
			"totalEntities": 900000,
		},
	}
	_, err = coll.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	require.NoError(t, err)

	// 2. Add incremental data (200k files) - total will be 1.1M, should trigger upgrade
	store.AddDirStats("100", "new1", 100000, 100000)
	store.AddDirStats("100", "new2", 100000, 100000)

	// 3. Trigger save - this should upgrade L1 because total > 1M
	store.SaveChangedCurrent()

	// 4. Verify L1 was upgraded (this should fail with current buggy logic)
	upgradedL1 := store.getCurrentL1()
	assert.Equal(t, "101", upgradedL1, "L1 should be upgraded when total (900k + 200k = 1.1M) exceeds limit")

	// 5. Verify it was saved to MongoDB
	store.curL1 = "" // Clear memory
	err = store.loadCurrentL1()
	require.NoError(t, err)
	assert.Equal(t, "101", store.getCurrentL1(), "Upgraded L1 should be persisted in MongoDB")
}

// Additional simple tests to improve coverage
func TestFormatBytesEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		bytes    int64
		expected string
	}{
		{
			name:     "Negative bytes",
			bytes:    -1024,
			expected: "0 B", // formatBytes handles negative as 0
		},
		{
			name:     "Very large number",
			bytes:    1099511627776, // 1 TB
			expected: "1.0 TB",      // formatBytes shows TB for this size
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatBytes(tt.bytes)
			if result != tt.expected {
				t.Errorf("formatBytes(%d) = %q, want %q", tt.bytes, result, tt.expected)
			}
		})
	}
}

func TestIsValidBoardEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		board    string
		expected bool
	}{
		{
			name:     "Uppercase board",
			board:    "RM",
			expected: false, // assuming case sensitive
		},
		{
			name:     "Mixed case board",
			board:    "Rm",
			expected: false,
		},
		{
			name:     "Special characters",
			board:    "r@m",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidBoard(tt.board)
			if result != tt.expected {
				t.Errorf("IsValidBoard(%q) = %v, want %v", tt.board, result, tt.expected)
			}
		})
	}
}

func TestSaveStatsToFile_Coverage(t *testing.T) {
	t.Skip("Skipping test that requires specific board configuration")
}

func TestSaveMetaInfoToFile_Coverage(t *testing.T) {
	t.Skip("Skipping test that requires specific board configuration")
}

func TestDirKeyStore_SetCustomDirectories(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store := &DirKeyStore{
		prefix: "TEST",
		dirMap: make(map[string]DirStats),
	}

	// 测试设置自定义目录
	customDirs := []string{"/tmp/test1", "/tmp/test2"}
	store.SetCustomDirectories(customDirs)

	// 检查自定义目录是否被正确设置
	store.mu.RLock()
	assert.Equal(t, 2, len(store.customDirs), "应该有2个自定义目录")
	assert.Equal(t, "/tmp/test1", store.customDirs[0], "第一个目录应该是 /tmp/test1")
	assert.Equal(t, "/tmp/test2", store.customDirs[1], "第二个目录应该是 /tmp/test2")
	store.mu.RUnlock()

	// 测试设置空目录
	store.SetCustomDirectories(nil)
	store.mu.RLock()
	assert.Equal(t, 0, len(store.customDirs), "自定义目录应该被清空")
	store.mu.RUnlock()

	// 测试设置包含空格的目录
	store.SetCustomDirectories([]string{" /tmp/test3 ", ""})
	store.mu.RLock()
	assert.Equal(t, 1, len(store.customDirs), "应该只有1个有效目录")
	assert.Equal(t, "/tmp/test3", store.customDirs[0], "空格应该被去除")
	store.mu.RUnlock()

	// 测试对nil store的调用
	var nilStore *DirKeyStore
	nilStore.SetCustomDirectories([]string{"/tmp/test"})
	// 如果没有panic，就通过测试
}

// 测试saveStatsToFile使用customDirs
func TestDirKeyStore_SaveStatsToFile_WithCustomDirs(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "golevelstore_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建目录结构
	customPath := filepath.Join(tempDir, "custom")
	require.NoError(t, os.MkdirAll(customPath, 0755))

	// 创建store实例
	backend := NewMongoBackend(coll)
	store := &DirKeyStore{
		prefix:  "CUSTOM",
		backend: backend,
		dirMap:  make(map[string]DirStats),
	}

	// 设置自定义目录
	store.SetCustomDirectories([]string{customPath})

	// 打印调试信息
	t.Logf("Custom directory set to: %s", customPath)
	store.mu.RLock()
	t.Logf("Store customDirs: %v", store.customDirs)
	store.mu.RUnlock()

	// 添加一些统计数据，直接修改 dirMap
	l1 := "100"
	l2 := "abc12"
	store.mu.Lock()
	store.dirMap[l1+"-"+l2] = DirStats{
		EntityAmount: 10,
		FileAmount:   20,
	}
	store.mu.Unlock()

	// 打印dirMap
	store.mu.RLock()
	t.Logf("Store dirMap: %v", store.dirMap)
	store.mu.RUnlock()

	// 创建l1Map
	l1Map := groupStatsByL1(store.dirMap)
	t.Logf("l1Map: %v", l1Map)

	// 调用saveStatsToFile
	err = store.saveStatsToFile(l1Map)
	if err != nil {
		t.Logf("saveStatsToFile error: %v", err)
	} else {
		t.Logf("saveStatsToFile succeeded")
	}
	assert.NoError(t, err)

	// 检查是否在自定义目录中创建了文件
	metaPath := filepath.Join(customPath, l1, "dir_stats.json")
	t.Logf("Looking for file at: %s", metaPath)
	fileInfo, err := os.Stat(metaPath)
	if err != nil {
		t.Logf("Stat error: %v", err)
	} else {
		t.Logf("File exists with size: %d", fileInfo.Size())
	}
	assert.NoError(t, err, "应该在自定义目录中创建dir_stats.json文件")

	// 读取文件内容并验证
	data, err := os.ReadFile(metaPath)
	assert.NoError(t, err)

	var meta DirMetaInfo
	err = json.Unmarshal(data, &meta)
	assert.NoError(t, err)

	// 验证统计数据
	assert.Equal(t, 10, meta.TotalEntities)
	assert.Equal(t, 20, meta.TotalFiles)
	assert.Contains(t, meta.L2Stats, l2)
	assert.Equal(t, 10, meta.L2Stats[l2].EntityAmount)
	assert.Equal(t, 20, meta.L2Stats[l2].FileAmount)
}

// TestDirKeyStore_NonImageBoard 测试非图片板块功能
func TestDirKeyStore_NonImageBoard(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// 创建一个 TEST 板块的 DirKeyStore 实例
	// TEST 板块是我们专门为测试非图片板块功能而添加的
	store, err := newTestDirKeyStore("TEST", coll, "")
	require.NoError(t, err, "应该能够为 TEST 板块创建 DirKeyStore")
	defer store.Close()

	// 设置自定义目录
	tempDir, err := os.MkdirTemp("", "test_non_image_board_*")
	require.NoError(t, err, "应该能够创建临时目录")
	defer os.RemoveAll(tempDir)

	customDir := filepath.Join(tempDir, "custom")
	err = os.MkdirAll(customDir, 0755)
	require.NoError(t, err, "应该能够创建自定义目录")

	t.Logf("自定义目录设置为: %s", customDir)

	// 为 TEST 板块设置自定义目录
	store.SetCustomDirectories([]string{customDir})

	// 添加统计数据
	l1 := "100" // 非图片板块使用基于统计的 L1 生成
	l2 := "abc12"

	// 手动创建 L1 目录来帮助测试
	l1Dir := filepath.Join(customDir, l1)
	err = os.MkdirAll(l1Dir, 0755)
	require.NoError(t, err, "应该能够创建 L1 目录")

	// 直接操作 dirMap 以确保有数据可保存
	store.mu.Lock()
	store.dirMap = map[string]DirStats{
		l1 + "-" + l2: {EntityAmount: 15, FileAmount: 30},
	}
	store.lastUpdateTs = time.Now()
	store.mu.Unlock()

	t.Logf("Store dirMap: %v", store.dirMap)

	// 创建一个临时的 l1Map 以便调试
	l1Map := map[string]map[string]DirStats{
		l1: {
			l2: {EntityAmount: 15, FileAmount: 30},
		},
	}
	t.Logf("l1Map: %v", l1Map)

	// 手动调用 saveStatsToFile，不再调用 SaveChangedCurrent 避免重复计算
	err = store.saveStatsToFile(l1Map)
	if err != nil {
		t.Logf("saveStatsToFile 失败: %v", err)
	} else {
		t.Logf("saveStatsToFile 成功")
	}

	// 检查统计文件是否被创建
	statsFile := filepath.Join(l1Dir, "dir_stats.json")
	t.Logf("查找文件: %s", statsFile)

	// 检查文件是否存在及大小
	if stat, err := os.Stat(statsFile); err != nil {
		t.Errorf("统计文件创建失败: %v", err)
	} else {
		t.Logf("文件存在，大小为: %d", stat.Size())

		// 读取并验证文件内容
		data, err := os.ReadFile(statsFile)
		if err != nil {
			t.Errorf("读取统计文件失败: %v", err)
		} else {
			var meta DirMetaInfo
			err = json.Unmarshal(data, &meta)
			if err != nil {
				t.Errorf("解析 JSON 失败: %v", err)
				t.Logf("文件内容: %s", string(data))
			} else {
				// 验证统计数据
				assert.Equal(t, 15, meta.TotalEntities, "实体总数应该是15")
				assert.Equal(t, 30, meta.TotalFiles, "文件总数应该是30")
				if l2Stats, ok := meta.L2Stats[l2]; !ok {
					t.Errorf("L2 统计数据不包含 %s", l2)
				} else {
					assert.Equal(t, 15, l2Stats.EntityAmount, "abc12 的实体数应该是15")
					assert.Equal(t, 30, l2Stats.FileAmount, "abc12 的文件数应该是30")
				}
			}
		}
	}
}

// TestDirKeyStore_WithEntryName 测试带entryName的新功能
func TestDirKeyStore_WithEntryName(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// 测试不同的entryName创建独立的store
	store1, err := newTestDirKeyStore("NEWBOARD", coll, "video", 5*time.Minute)
	require.NoError(t, err, "应该能够创建NEWBOARD_video store")
	defer store1.Close()

	store2, err := newTestDirKeyStore("NEWBOARD", coll, "file", 5*time.Minute)
	require.NoError(t, err, "应该能够创建NEWBOARD_file store")
	defer store2.Close()

	// 验证boardKey不同
	assert.Equal(t, "NEWBOARD", store1.prefix)
	assert.Equal(t, "video", store1.entryName)
	assert.Equal(t, "NEWBOARD_video", store1.boardKey)

	assert.Equal(t, "NEWBOARD", store2.prefix)
	assert.Equal(t, "file", store2.entryName)
	assert.Equal(t, "NEWBOARD_file", store2.boardKey)

	// 测试兼容模式（不带entryName）
	store3, err := newTestDirKeyStore("TRB", coll, "", 5*time.Minute)
	require.NoError(t, err, "应该能够创建兼容模式的TRB store")
	defer store3.Close()

	assert.Equal(t, "TRB", store3.prefix)
	assert.Equal(t, "", store3.entryName)
	assert.Equal(t, "TRB", store3.boardKey)

	// 添加统计数据到不同的store
	store1.AddDirStats("100", "abc12", 10, 20)
	store2.AddDirStats("100", "abc12", 15, 30)

	// 验证统计数据是独立的
	store1.mu.RLock()
	stats1 := store1.dirMap["100-abc12"]
	store1.mu.RUnlock()

	store2.mu.RLock()
	stats2 := store2.dirMap["100-abc12"]
	store2.mu.RUnlock()

	assert.Equal(t, 10, stats1.EntityAmount)
	assert.Equal(t, 20, stats1.FileAmount)

	assert.Equal(t, 15, stats2.EntityAmount)
	assert.Equal(t, 30, stats2.FileAmount)

	t.Logf("Store1 (NEWBOARD_video) stats: %+v", stats1)
	t.Logf("Store2 (NEWBOARD_file) stats: %+v", stats2)
}

// TestDirKeyStore_MongoDB_DataIsolation 测试MongoDB数据隔离
func TestDirKeyStore_MongoDB_DataIsolation(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// 创建两个不同entryName的store
	store1, err := newTestDirKeyStore("NEWBOARD", coll, "video")
	require.NoError(t, err)
	defer store1.Close()

	store2, err := newTestDirKeyStore("NEWBOARD", coll, "file")
	require.NoError(t, err)
	defer store2.Close()

	// 添加统计数据并保存到数据库
	store1.AddDirStats("100", "abc12", 10, 20)
	store2.AddDirStats("100", "abc12", 15, 30)

	// 强制保存到数据库
	store1.SaveChangedCurrent()
	store2.SaveChangedCurrent()

	// 验证MongoDB中的数据是独立的
	ctx := context.Background()

	// 检查store1的数据
	var result1 struct {
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}
	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "NEWBOARD_video"},
			{Key: "l1", Value: "100"},
		}},
	}).Decode(&result1)
	require.NoError(t, err, "应该能找到NEWBOARD_video的数据")

	// 检查store2的数据
	var result2 struct {
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}
	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "NEWBOARD_file"},
			{Key: "l1", Value: "100"},
		}},
	}).Decode(&result2)
	require.NoError(t, err, "应该能找到NEWBOARD_file的数据")

	// 验证数据隔离
	assert.Equal(t, 20, result1.TotalFiles)
	assert.Equal(t, 10, result1.TotalEntities)
	assert.Equal(t, 30, result2.TotalFiles)
	assert.Equal(t, 15, result2.TotalEntities)

	t.Logf("NEWBOARD_video MongoDB data: %+v", result1)
	t.Logf("NEWBOARD_file MongoDB data: %+v", result2)
}

// TestDirKeyStore_FixedParameters 测试新的固定参数API
func TestDirKeyStore_FixedParameters(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	tests := []struct {
		name           string
		prefix         string
		entryName      string
		updateInterval []time.Duration
		expectedEntry  string
		expectedPrefix string
		expectedBoard  string
	}{
		{
			name:           "只有entryName为空",
			prefix:         "TRB",
			entryName:      "",
			updateInterval: nil,
			expectedEntry:  "",
			expectedPrefix: "TRB",
			expectedBoard:  "TRB",
		},
		{
			name:           "只有entryName为空，带updateInterval",
			prefix:         "TRB",
			entryName:      "",
			updateInterval: []time.Duration{5 * time.Minute},
			expectedEntry:  "",
			expectedPrefix: "TRB",
			expectedBoard:  "TRB",
		},
		{
			name:           "有entryName",
			prefix:         "NEWBOARD",
			entryName:      "video",
			updateInterval: nil,
			expectedEntry:  "video",
			expectedPrefix: "NEWBOARD",
			expectedBoard:  "NEWBOARD_video",
		},
		{
			name:           "entryName和updateInterval",
			prefix:         "NEWBOARD",
			entryName:      "file",
			updateInterval: []time.Duration{10 * time.Minute},
			expectedEntry:  "file",
			expectedPrefix: "NEWBOARD",
			expectedBoard:  "NEWBOARD_file",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var store *DirKeyStore
			var err error

			if len(tt.updateInterval) > 0 {
				store, err = newTestDirKeyStore(tt.prefix, coll, tt.entryName, tt.updateInterval[0])
			} else {
				store, err = newTestDirKeyStore(tt.prefix, coll, tt.entryName)
			}

			require.NoError(t, err, "应该能够创建store")
			defer store.Close()

			assert.Equal(t, tt.expectedPrefix, store.prefix)
			assert.Equal(t, tt.expectedEntry, store.entryName)
			assert.Equal(t, tt.expectedBoard, store.boardKey)
		})
	}
}

// TestDirKeyStore_L1StateIsolation 测试L1状态的独立性
func TestDirKeyStore_L1StateIsolation(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// 创建两个不同entryName的store
	store1, err := newTestDirKeyStore("NEWBOARD", coll, "video")
	require.NoError(t, err)
	defer store1.Close()

	store2, err := newTestDirKeyStore("NEWBOARD", coll, "file")
	require.NoError(t, err)
	defer store2.Close()

	// 验证初始L1状态
	l1_1 := store1.getCurrentL1()
	l1_2 := store2.getCurrentL1()

	t.Logf("Store1 (NEWBOARD_video) initial L1: %s", l1_1)
	t.Logf("Store2 (NEWBOARD_file) initial L1: %s", l1_2)

	// 两个store应该有独立的L1状态
	assert.Equal(t, "100", l1_1, "NEWBOARD_video应该从100开始")
	assert.Equal(t, "100", l1_2, "NEWBOARD_file应该从100开始")

	// 验证MongoDB中的L1状态文档是独立的
	ctx := context.Background()

	// 检查store1的L1状态文档
	var result1 struct {
		L1 string `bson:"l1"`
	}
	err1 := coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "NEWBOARD_video"},
			{Key: "type", Value: "current_l1"},
		}},
	}).Decode(&result1)

	// 检查store2的L1状态文档
	var result2 struct {
		L1 string `bson:"l1"`
	}
	err2 := coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "NEWBOARD_file"},
			{Key: "type", Value: "current_l1"},
		}},
	}).Decode(&result2)

	// 如果文档存在，验证它们是独立的
	if err1 == nil && err2 == nil {
		t.Logf("NEWBOARD_video L1 in MongoDB: %s", result1.L1)
		t.Logf("NEWBOARD_file L1 in MongoDB: %s", result2.L1)

		assert.Equal(t, result1.L1, l1_1, "MongoDB中的L1状态应该与store中一致")
		assert.Equal(t, result2.L1, l1_2, "MongoDB中的L1状态应该与store中一致")
	} else {
		t.Logf("L1状态文档可能还未创建 (err1: %v, err2: %v)", err1, err2)
	}

	// 验证注册表中的独立性
	// 这里我们无法直接访问globalStores，但可以通过功能验证
	// 如果L1计算正确，说明注册表是独立的
	assert.NotEmpty(t, l1_1, "L1不应该为空")
	assert.NotEmpty(t, l1_2, "L1不应该为空")
}

// TestDirKeyStore_ParameterValidation 测试参数验证
func TestDirKeyStore_ParameterValidation(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// 测试有效参数
	store1, err := newTestDirKeyStore("TRB", coll, "", 5*time.Minute)
	require.NoError(t, err, "有效的time.Duration参数应该被接受")
	defer store1.Close()

	store2, err := newTestDirKeyStore("NEWBOARD", coll, "video")
	require.NoError(t, err, "有效的string参数应该被接受")
	defer store2.Close()

	store3, err := newTestDirKeyStore("NEWBOARD", coll, "file", 10*time.Minute)
	require.NoError(t, err, "有效的string和time.Duration组合应该被接受")
	defer store3.Close()

	// 测试边界情况
	// 测试负数的updateInterval（应该使用默认值）
	store4, err := newTestDirKeyStore("TRB", coll, "", -5*time.Minute)
	require.NoError(t, err, "负数的updateInterval应该被接受并使用默认值")
	defer store4.Close()

	// 测试零值的updateInterval（应该使用默认值）
	store5, err := newTestDirKeyStore("TRB", coll, "", 0)
	require.NoError(t, err, "零值的updateInterval应该被接受并使用默认值")
	defer store5.Close()
}

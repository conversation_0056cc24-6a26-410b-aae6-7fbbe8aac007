package levelStore

import (
	"context"
	"errors"
	"strings"

	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoBackend implements DirStoreBackend backed by a MongoDB collection.
type MongoBackend struct {
	coll *gomongo.MongoCollection
}

func NewMongoBackend(coll *gomongo.MongoCollection) *MongoBackend {
	if coll == nil {
		panic("NewMongoBackend: collection cannot be nil")
	}
	return &MongoBackend{coll: coll}
}

func isNoDoc(err error) bool {
	if err == nil {
		return false
	}
	// 优先使用官方驱动的错误哨兵
	if errors.Is(err, mongo.ErrNoDocuments) {
		return true
	}
	// 兼容 gomongo/其他包装后的错误信息
	return strings.Contains(err.<PERSON><PERSON>r(), "no documents in result")
}

func (m *MongoBackend) LoadCurrentL1(ctx context.Context, boardKey string) (string, error) {
	var out struct {
		L1 string `bson:"l1"`
	}
	err := m.coll.FindOne(ctx, bson.D{{Key: "_id", Value: bson.D{{Key: "board", Value: boardKey}, {Key: "type", Value: "current_l1"}}}}).Decode(&out)
	if err != nil {
		if isNoDoc(err) {
			return "", ErrNotFound
		}
		return "", err
	}
	return out.L1, nil
}

func (m *MongoBackend) SaveCurrentL1(ctx context.Context, boardKey, l1 string) error {
	filter := bson.D{{Key: "_id", Value: bson.D{{Key: "board", Value: boardKey}, {Key: "type", Value: "current_l1"}}}}
	update := bson.D{{Key: "$set", Value: bson.D{{Key: "l1", Value: l1}}}}
	_, err := m.coll.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	return err
}

func (m *MongoBackend) LoadL1(ctx context.Context, boardKey, l1 string) (L1Record, error) {
	var d struct {
		DirArray      []string            `bson:"dirArray"`
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}
	err := m.coll.FindOne(ctx, bson.D{{Key: "_id", Value: bson.D{{Key: "board", Value: boardKey}, {Key: "l1", Value: l1}}}}).Decode(&d)
	if err != nil {
		if isNoDoc(err) {
			return L1Record{}, ErrNotFound
		}
		return L1Record{}, err
	}
	if d.L2Stats == nil {
		d.L2Stats = map[string]DirStats{}
	}
	return L1Record{DirArray: d.DirArray, L2Stats: d.L2Stats, TotalFiles: d.TotalFiles, TotalEntities: d.TotalEntities}, nil
}

func (m *MongoBackend) SaveL1(ctx context.Context, boardKey, l1 string, rec L1Record) error {
	filter := bson.D{{Key: "_id", Value: bson.D{{Key: "board", Value: boardKey}, {Key: "l1", Value: l1}}}}
	update := bson.M{"$set": bson.M{
		"dirArray":      rec.DirArray,
		"l2Stats":       rec.L2Stats,
		"totalFiles":    rec.TotalFiles,
		"totalEntities": rec.TotalEntities,
	}}
	_, err := m.coll.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	return err
}

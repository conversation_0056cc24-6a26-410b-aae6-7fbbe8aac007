# GoSpeedMeter

GoSpeedMeter is a Go package that provides a simple and efficient way to monitor and measure performance metrics in your applications. It offers two main components: **SpeedMeter** for simple interval-based measurements and **SpeedAggregate** for advanced sliding window calculations.

## Features

### SpeedMeter (Simple)
- Track multiple metrics simultaneously
- Configurable measurement intervals
- Customizable callback functions
- Support for various time units (ms, s, m, h, d)
- Thread-safe operations
- Automatic number formatting with K/M/B/T units
- Time estimation for target values

### SpeedAggregate (Advanced)
- **Sliding window speed calculations** - More accurate than interval-based measurements
- **Automatic data cleanup** - Removes expired data points automatically
- **High-performance concurrent access** - Optimized with read-write locks and data copying
- **Configurable window duration** - Set custom time windows for calculations
- **Minimum data points requirement** - Ensures calculation accuracy
- **Binary search optimization** - Fast data point lookup for large datasets
- **Periodic callbacks** - Trigger custom functions based on data point count

## Installation

```bash
go get github.com/real-rm/gospeedmeter
```

## Usage

### SpeedMeter - Basic Usage

```go
package main

import (
    "log"
    "time"

    "github.com/real-rm/gospeedmeter"
)

func main() {
    // Create a new speed meter with default options
    sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

    // Track a metric
    sm.Check("requests", 1)

    // Get current speed
    speed := sm.GetSpeed(gospeedmeter.UnitS)
    log.Printf("Current speed: %v requests/second", speed["requests"])

    // Get formatted string representation
    log.Printf("Metrics: %s", sm.ToString(gospeedmeter.UnitM, nil))
}
```

### SpeedAggregate - Basic Usage

```go
package main

import (
    "log"
    "time"

    "github.com/real-rm/gospeedmeter"
)

func main() {
    // Create a new speed aggregate with default options
    sa := gospeedmeter.NewSpeedAggregate(gospeedmeter.SpeedAggregateOptions{})
    defer sa.Stop() // Important: stop the background cleanup routine

    // Add data points
    sa.AddDataPoint("downloads", 1024) // 1KB downloaded
    time.Sleep(100 * time.Millisecond)
    sa.AddDataPoint("downloads", 2048) // 2KB downloaded

    // Get current speed for a specific metric
    speed := sa.GetCurrentSpeed("downloads", gospeedmeter.UnitS)
    log.Printf("Download speed: %.2f bytes/second", speed)

    // Get all speeds
    allSpeeds := sa.GetAllSpeeds(gospeedmeter.UnitS)
    log.Printf("All speeds: %v", allSpeeds)

    // Get formatted string representation
    log.Printf("Metrics: %s", sa.ToString(gospeedmeter.UnitS, nil))
}
```

### SpeedMeter - Advanced Usage

```go
// Create a speed meter with custom options
sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{
    Values: map[string]float64{
        "requests": 0,
        "errors": 0,
    },
    IntervalCallback: func(sm *gospeedmeter.SpeedMeter) {
        // Log metrics every 1000 operations
        log.Printf("Performance metrics: %s", sm.ToString(gospeedmeter.UnitM, nil))
    },
    IntervalTriggerCount: 1000,
})

// Track multiple metrics
sm.Check("requests", 1)

// Example of error handling (assuming some operation that might fail)
resp, err := someOperation()
if err != nil {
    sm.Check("errors", 1)
    log.Printf("Error: %v", err)
}

// Estimate time to reach target
targetValue := 1000.0
estimate := sm.Estimate("requests", targetValue, gospeedmeter.UnitM)
log.Printf("Estimated time to reach %v requests: %.2f minutes", targetValue, estimate)
```

### SpeedAggregate - Advanced Usage

```go
// Create a speed aggregate with custom options
sa := gospeedmeter.NewSpeedAggregate(gospeedmeter.SpeedAggregateOptions{
    WindowDuration:       30 * time.Second,  // 30-second sliding window
    CleanupInterval:      5 * time.Second,   // Clean up every 5 seconds
    MinDataPoints:        3,                 // Need at least 3 data points
    IntervalTriggerCount: 100,               // Trigger callback every 100 data points
    IntervalCallback: func(sa *gospeedmeter.SpeedAggregate) {
        // Custom callback function
        speeds := sa.GetAllSpeeds(gospeedmeter.UnitS)
        log.Printf("Current speeds: %v", speeds)
    },
})
defer sa.Stop()

// Simulate file download tracking
for i := 0; i < 1000; i++ {
    // Add download progress
    sa.AddDataPoint("downloads", 1024) // 1KB per operation

    // Add upload progress
    sa.AddDataPoint("uploads", 512)    // 512B per operation

    time.Sleep(10 * time.Millisecond) // Simulate work
}

// Get total counters
totals := sa.GetTotalCounters()
log.Printf("Total downloaded: %.0f bytes", totals["downloads"])
log.Printf("Total uploaded: %.0f bytes", totals["uploads"])

// Get data point counts
counts := sa.GetDataPointCount()
log.Printf("Download data points: %d", counts["downloads"])

// Use with estimation
toBeEstimated := map[string]float64{
    "downloads": 1024 * 1024, // Target: 1MB
}
result := sa.ToString(gospeedmeter.UnitS, toBeEstimated)
log.Printf("Progress with estimation: %s", result)
```

### SpeedMeter vs SpeedAggregate

| Feature | SpeedMeter | SpeedAggregate |
|---------|------------|----------------|
| **Calculation Method** | Interval-based | Sliding window |
| **Accuracy** | Good for steady rates | High accuracy for variable rates |
| **Memory Usage** | Low | Higher (stores data points) |
| **Performance** | Very fast | Optimized for concurrent access |
| **Data Cleanup** | Manual reset | Automatic cleanup |
| **Configuration** | Simple | Highly configurable |
| **Use Case** | Simple metrics tracking | Advanced performance monitoring |
| **Background Tasks** | None | Cleanup goroutine |

**When to use SpeedMeter:**
- Simple applications with steady data rates
- Low memory requirements
- Minimal configuration needed

**When to use SpeedAggregate:**
- Applications with variable data rates
- Need accurate speed calculations over time windows
- High-frequency data collection
- Advanced monitoring and analytics

### Available Time Units

- `UnitMS`: Milliseconds
- `UnitS`: Seconds
- `UnitM`: Minutes
- `UnitH`: Hours
- `UnitD`: Days

### Number Formatting

The package automatically formats large numbers using K/M/B/T units:
- K: Thousands (1,000)
- M: Millions (1,000,000)
- B: Billions (1,000,000,000)
- T: Trillions (1,000,000,000,000)

Example output:
```
requests(1000):1.5K/s
errors(10):0.1K/s
```

## API Reference

### SpeedMeter

```go
type SpeedMeter struct {
    // ... internal fields
}

type SpeedMeterOptions struct {
    Values               map[string]float64
    IntervalCallback     func(*SpeedMeter)
    IntervalTriggerCount int
}
```

#### SpeedMeter Methods

- `NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter`: Create a new speed meter
- `Reset(values ...map[string]float64)`: Reset the speed meter with optional initial values
- `Check(name string, value float64)`: Update a metric value
- `GetSpeed(unit Unit) map[string]float64`: Get current speeds for all metrics
- `ToString(unit Unit, toBeEstimated map[string]float64) string`: Get formatted string representation
- `Estimate(name string, targetValue float64, unit Unit) float64`: Estimate time to reach target
- `GetCounters() map[string]float64`: Get current counter values

### SpeedAggregate

```go
type DataPoint struct {
    Timestamp time.Time
    Value     float64
}

type SpeedAggregate struct {
    // ... internal fields
}

type SpeedAggregateOptions struct {
    WindowDuration       time.Duration         // Sliding window duration (default: 60s)
    CleanupInterval      time.Duration         // Cleanup interval (default: 10s)
    IntervalCallback     func(*SpeedAggregate) // Periodic callback function
    IntervalTriggerCount int                   // Callback trigger count (default: 100)
    MinDataPoints        int                   // Minimum data points for calculation (default: 2)
}
```

#### SpeedAggregate Methods

- `NewSpeedAggregate(options SpeedAggregateOptions) *SpeedAggregate`: Create a new speed aggregate
- `AddDataPoint(name string, value float64)`: Add a data point for the specified metric
- `GetCurrentSpeed(name string, unit Unit) float64`: Get current speed for a specific metric
- `GetAllSpeeds(unit Unit) map[string]float64`: Get current speeds for all metrics
- `ToString(unit Unit, toBeEstimated map[string]float64) string`: Get formatted string representation
- `GetTotalCounters() map[string]float64`: Get total accumulated values for all metrics
- `GetDataPointCount() map[string]int`: Get number of data points for each metric
- `Reset()`: Clear all data points
- `Stop()`: Stop the background cleanup routine (important for cleanup)

## Thread Safety

Both SpeedMeter and SpeedAggregate are fully thread-safe:

- **SpeedMeter**: Uses mutex locks to prevent race conditions when updating metrics from multiple goroutines
- **SpeedAggregate**: Uses read-write locks (RWMutex) for optimized concurrent access:
  - Multiple goroutines can read speeds simultaneously
  - Data copying prevents race conditions during calculations
  - Background cleanup routine runs safely in parallel

## Performance Considerations

### SpeedAggregate Optimizations

- **Binary search**: Fast lookup of data points within time windows
- **Data copying**: Prevents long lock holding during calculations
- **Efficient cleanup**: Automatic removal of expired data points
- **Memory management**: Configurable window duration to control memory usage

### Best Practices

1. **Always call `Stop()`** on SpeedAggregate instances to clean up background goroutines
2. **Configure appropriate window duration** based on your data frequency
3. **Set reasonable cleanup intervals** to balance performance and memory usage
4. **Use appropriate minimum data points** to ensure calculation accuracy

## License

MIT License
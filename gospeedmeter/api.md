<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gospeedmeter

```go
import "github.com/real-rm/gospeedmeter"
```

## Index

- [Constants](<#constants>)
- [type DataPoint](<#DataPoint>)
- [type SpeedAggregate](<#SpeedAggregate>)
  - [func NewSpeedAggregate\(options SpeedAggregateOptions\) \*SpeedAggregate](<#NewSpeedAggregate>)
  - [func \(sa \*SpeedAggregate\) AddDataPoint\(name string, value float64\)](<#SpeedAggregate.AddDataPoint>)
  - [func \(sa \*SpeedAggregate\) GetAllSpeeds\(unit Unit\) map\[string\]float64](<#SpeedAggregate.GetAllSpeeds>)
  - [func \(sa \*SpeedAggregate\) GetCurrentSpeed\(name string, unit Unit\) float64](<#SpeedAggregate.GetCurrentSpeed>)
  - [func \(sa \*SpeedAggregate\) GetDataPointCount\(\) map\[string\]int](<#SpeedAggregate.GetDataPointCount>)
  - [func \(sa \*SpeedAggregate\) GetTotalCounters\(\) map\[string\]float64](<#SpeedAggregate.GetTotalCounters>)
  - [func \(sa \*SpeedAggregate\) Reset\(\)](<#SpeedAggregate.Reset>)
  - [func \(sa \*SpeedAggregate\) Stop\(\)](<#SpeedAggregate.Stop>)
  - [func \(sa \*SpeedAggregate\) ToString\(unit Unit, toBeEstimated map\[string\]float64\) string](<#SpeedAggregate.ToString>)
- [type SpeedAggregateOptions](<#SpeedAggregateOptions>)
- [type SpeedMeter](<#SpeedMeter>)
  - [func NewSpeedMeter\(options SpeedMeterOptions\) \*SpeedMeter](<#NewSpeedMeter>)
  - [func \(sm \*SpeedMeter\) Check\(name string, value float64\)](<#SpeedMeter.Check>)
  - [func \(sm \*SpeedMeter\) Estimate\(name string, targetValue float64, unit Unit\) float64](<#SpeedMeter.Estimate>)
  - [func \(sm \*SpeedMeter\) GetCounters\(\) map\[string\]float64](<#SpeedMeter.GetCounters>)
  - [func \(sm \*SpeedMeter\) GetSpeed\(unit Unit\) map\[string\]float64](<#SpeedMeter.GetSpeed>)
  - [func \(sm \*SpeedMeter\) Reset\(values ...map\[string\]float64\)](<#SpeedMeter.Reset>)
  - [func \(sm \*SpeedMeter\) ToString\(unit Unit, toBeEstimated map\[string\]float64\) string](<#SpeedMeter.ToString>)
- [type SpeedMeterOptions](<#SpeedMeterOptions>)
- [type Unit](<#Unit>)


## Constants

<a name="AMOUNT_UNIT"></a>

```go
const AMOUNT_UNIT = "KMBT"
```

<a name="DataPoint"></a>
## type [DataPoint](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L12-L16>)

DataPoint represents a single data point with timestamp and value

```go
type DataPoint struct {
    Timestamp time.Time
    Value     float64
}
```

<a name="SpeedAggregate"></a>
## type [SpeedAggregate](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L27-L43>)

SpeedAggregate handles sliding window speed calculations for multiple metrics

```go
type SpeedAggregate struct {
    // contains filtered or unexported fields
}
```

<a name="NewSpeedAggregate"></a>
### func [NewSpeedAggregate](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L45>)

```go
func NewSpeedAggregate(options SpeedAggregateOptions) *SpeedAggregate
```

NewSpeedAggregate creates a new SpeedAggregate instance

<a name="SpeedAggregate.AddDataPoint"></a>
### func \(\*SpeedAggregate\) [AddDataPoint](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L96>)

```go
func (sa *SpeedAggregate) AddDataPoint(name string, value float64)
```

AddDataPoint adds a new data point for the specified metric

<a name="SpeedAggregate.GetAllSpeeds"></a>
### func \(\*SpeedAggregate\) [GetAllSpeeds](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L139>)

```go
func (sa *SpeedAggregate) GetAllSpeeds(unit Unit) map[string]float64
```

GetAllSpeeds returns the current speeds for all metrics in the specified unit

<a name="SpeedAggregate.GetCurrentSpeed"></a>
### func \(\*SpeedAggregate\) [GetCurrentSpeed](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L121>)

```go
func (sa *SpeedAggregate) GetCurrentSpeed(name string, unit Unit) float64
```

GetCurrentSpeed returns the current speed for a specific metric in the specified unit

<a name="SpeedAggregate.GetDataPointCount"></a>
### func \(\*SpeedAggregate\) [GetDataPointCount](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L374>)

```go
func (sa *SpeedAggregate) GetDataPointCount() map[string]int
```

GetDataPointCount returns the number of data points for each metric

<a name="SpeedAggregate.GetTotalCounters"></a>
### func \(\*SpeedAggregate\) [GetTotalCounters](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L361>)

```go
func (sa *SpeedAggregate) GetTotalCounters() map[string]float64
```

GetTotalCounters returns the total accumulated values for all metrics

<a name="SpeedAggregate.Reset"></a>
### func \(\*SpeedAggregate\) [Reset](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L387>)

```go
func (sa *SpeedAggregate) Reset()
```

Reset clears all data points and resets all total counters

<a name="SpeedAggregate.Stop"></a>
### func \(\*SpeedAggregate\) [Stop](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L285>)

```go
func (sa *SpeedAggregate) Stop()
```

Stop stops the background cleanup routine

<a name="SpeedAggregate.ToString"></a>
### func \(\*SpeedAggregate\) [ToString](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L299>)

```go
func (sa *SpeedAggregate) ToString(unit Unit, toBeEstimated map[string]float64) string
```

ToString returns a formatted string representation of the speed aggregate

<a name="SpeedAggregateOptions"></a>
## type [SpeedAggregateOptions](<https://github.com/real-rm/gospeedmeter/blob/main/speed_aggregate.go#L18-L25>)

SpeedAggregateOptions defines the configuration options for SpeedAggregate

```go
type SpeedAggregateOptions struct {
    WindowDuration       time.Duration         // 滑动窗口时间长度，默认60秒
    CleanupInterval      time.Duration         // 清理过期数据的间隔，默认10秒
    IntervalCallback     func(*SpeedAggregate) // 定期回调函数
    IntervalTriggerCount int                   // 触发回调的数据点数量
    MinDataPoints        int                   // 计算速度所需的最小数据点数，默认2
}
```

<a name="SpeedMeter"></a>
## type [SpeedMeter](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L34-L41>)

SpeedMeter handles speed calculations for multiple meters

```go
type SpeedMeter struct {
    // contains filtered or unexported fields
}
```

<a name="NewSpeedMeter"></a>
### func [NewSpeedMeter](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L51>)

```go
func NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter
```

NewSpeedMeter creates a new SpeedMeter instance

<a name="SpeedMeter.Check"></a>
### func \(\*SpeedMeter\) [Check](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L87>)

```go
func (sm *SpeedMeter) Check(name string, value float64)
```

Check updates a single meter value and triggers callback if needed

<a name="SpeedMeter.Estimate"></a>
### func \(\*SpeedMeter\) [Estimate](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L138>)

```go
func (sm *SpeedMeter) Estimate(name string, targetValue float64, unit Unit) float64
```

Estimate calculates estimated time to reach target value for a single meter

<a name="SpeedMeter.GetCounters"></a>
### func \(\*SpeedMeter\) [GetCounters](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L148>)

```go
func (sm *SpeedMeter) GetCounters() map[string]float64
```

GetCounters returns current counter values

<a name="SpeedMeter.GetSpeed"></a>
### func \(\*SpeedMeter\) [GetSpeed](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L100>)

```go
func (sm *SpeedMeter) GetSpeed(unit Unit) map[string]float64
```

GetSpeed returns the current speed for all meters in the specified unit

<a name="SpeedMeter.Reset"></a>
### func \(\*SpeedMeter\) [Reset](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L77>)

```go
func (sm *SpeedMeter) Reset(values ...map[string]float64)
```

Reset resets the speed meter with optional initial values

<a name="SpeedMeter.ToString"></a>
### func \(\*SpeedMeter\) [ToString](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L119>)

```go
func (sm *SpeedMeter) ToString(unit Unit, toBeEstimated map[string]float64) string
```

ToString returns a formatted string representation of the speed meter

<a name="SpeedMeterOptions"></a>
## type [SpeedMeterOptions](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L44-L48>)

SpeedMeterOptions defines the configuration options for SpeedMeter

```go
type SpeedMeterOptions struct {
    Values               map[string]float64
    IntervalCallback     func(*SpeedMeter)
    IntervalTriggerCount int
}
```

<a name="Unit"></a>
## type [Unit](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L13>)

Unit represents time units for speed calculation

```go
type Unit string
```

<a name="UnitMS"></a>

```go
const (
    UnitMS Unit = "ms"
    UnitS  Unit = "s"
    UnitM  Unit = "m"
    UnitH  Unit = "h"
    UnitD  Unit = "d"
)
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)

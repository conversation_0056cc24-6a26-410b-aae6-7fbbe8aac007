package gospeedmeter

import (
	"fmt"
	"sort"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
)

// DataPoint represents a single data point with timestamp and value
type DataPoint struct {
	Timestamp time.Time
	Value     float64
}

// SpeedAggregateOptions defines the configuration options for SpeedAggregate
type SpeedAggregateOptions struct {
	WindowDuration       time.Duration         // 滑动窗口时间长度，默认60秒
	CleanupInterval      time.Duration         // 清理过期数据的间隔，默认10秒
	IntervalCallback     func(*SpeedAggregate) // 定期回调函数
	IntervalTriggerCount int                   // 触发回调的数据点数量
	MinDataPoints        int                   // 计算速度所需的最小数据点数，默认2
}

// SpeedAggregate handles sliding window speed calculations for multiple metrics
type SpeedAggregate struct {
	dataPoints           map[string][]DataPoint // 每个指标的数据点列表
	totalCounters        map[string]float64     // 每个指标的总计数器（优化GetTotalCounters性能）
	mu                   sync.RWMutex           // 读写锁
	windowDuration       time.Duration          // 滑动窗口时间长度
	cleanupInterval      time.Duration          // 清理间隔
	intervalCallback     func(*SpeedAggregate)  // 回调函数
	intervalTriggerCount int                    // 触发回调的计数
	minDataPoints        int                    // 最小数据点数
	counter              int                    // 总计数器
	lastCleanup          time.Time              // 上次清理时间
	stopCleanup          chan struct{}          // 停止清理的信号
	cleanupRunning       bool                   // 清理是否正在运行
	cleanupMu            sync.Mutex             // 保护cleanupRunning的互斥锁
	cleanupWg            sync.WaitGroup         // 等待清理goroutine完全停止
}

// NewSpeedAggregate creates a new SpeedAggregate instance
func NewSpeedAggregate(options SpeedAggregateOptions) *SpeedAggregate {
	// 设置默认值
	windowDuration := 60 * time.Second
	cleanupInterval := 10 * time.Second
	intervalTriggerCount := 100
	minDataPoints := 2

	if options.WindowDuration > 0 {
		windowDuration = options.WindowDuration
	}
	if options.CleanupInterval > 0 {
		cleanupInterval = options.CleanupInterval
	}
	if options.IntervalTriggerCount > 0 {
		intervalTriggerCount = options.IntervalTriggerCount
	}
	if options.MinDataPoints > 0 {
		minDataPoints = options.MinDataPoints
	}

	// 默认回调函数 - 轻量级实现，避免频繁的复杂计算
	intervalCallback := func(sa *SpeedAggregate) {
		// 简单记录数据点数量，避免复杂的速度计算
		counts := sa.GetDataPointCount()
		if len(counts) > 0 {
			golog.Info(fmt.Sprintf("SpeedAggregate: %d metrics active", len(counts)))
		}
	}
	if options.IntervalCallback != nil {
		intervalCallback = options.IntervalCallback
	}

	sa := &SpeedAggregate{
		dataPoints:           make(map[string][]DataPoint),
		totalCounters:        make(map[string]float64),
		windowDuration:       windowDuration,
		cleanupInterval:      cleanupInterval,
		intervalCallback:     intervalCallback,
		intervalTriggerCount: intervalTriggerCount,
		minDataPoints:        minDataPoints,
		lastCleanup:          time.Now(),
		stopCleanup:          make(chan struct{}),
	}

	// 启动后台清理 goroutine
	sa.startCleanupRoutine()

	return sa
}

// AddDataPoint adds a new data point for the specified metric
func (sa *SpeedAggregate) AddDataPoint(name string, value float64) {
	now := time.Now()
	dataPoint := DataPoint{
		Timestamp: now,
		Value:     value,
	}

	var shouldTriggerCallback bool
	var counter int

	sa.mu.Lock()
	sa.dataPoints[name] = append(sa.dataPoints[name], dataPoint)
	sa.totalCounters[name] += value
	sa.counter++
	counter = sa.counter
	shouldTriggerCallback = sa.intervalCallback != nil && counter%sa.intervalTriggerCount == 0
	sa.mu.Unlock()

	// 在锁外触发回调，避免死锁
	if shouldTriggerCallback {
		sa.intervalCallback(sa)
	}
}

// GetCurrentSpeed returns the current speed for a specific metric in the specified unit
func (sa *SpeedAggregate) GetCurrentSpeed(name string, unit Unit) float64 {
	sa.mu.RLock()
	originalDataPoints, exists := sa.dataPoints[name]
	if !exists || len(originalDataPoints) < sa.minDataPoints {
		sa.mu.RUnlock()
		return 0
	}

	// 创建切片的深拷贝，避免并发访问问题
	dataPoints := make([]DataPoint, len(originalDataPoints))
	copy(dataPoints, originalDataPoints)
	sa.mu.RUnlock()

	// 现在可以安全地在无锁状态下计算速度
	return sa.calculateSpeedUnsafe(dataPoints, unit)
}

// GetAllSpeeds returns the current speeds for all metrics in the specified unit
func (sa *SpeedAggregate) GetAllSpeeds(unit Unit) map[string]float64 {
	// 在锁内快速复制数据，减少锁持有时间
	sa.mu.RLock()
	dataPointsCopy := make(map[string][]DataPoint)
	for name, dataPoints := range sa.dataPoints {
		if len(dataPoints) >= sa.minDataPoints {
			// 创建数据点的深拷贝，避免并发访问问题
			copied := make([]DataPoint, len(dataPoints))
			copy(copied, dataPoints)
			dataPointsCopy[name] = copied
		}
	}
	sa.mu.RUnlock()

	// 在锁外计算速度，避免长时间持锁
	speeds := make(map[string]float64)
	for name, dataPoints := range dataPointsCopy {
		speeds[name] = sa.calculateSpeedUnsafe(dataPoints, unit)
	}

	// 为没有足够数据点的指标设置速度为0
	sa.mu.RLock()
	for name := range sa.dataPoints {
		if _, exists := speeds[name]; !exists {
			speeds[name] = 0
		}
	}
	sa.mu.RUnlock()

	return speeds
}

// calculateSpeedUnsafe calculates speed without acquiring locks (assumes caller holds lock)
func (sa *SpeedAggregate) calculateSpeedUnsafe(dataPoints []DataPoint, unit Unit) float64 {
	if len(dataPoints) < sa.minDataPoints {
		return 0
	}

	now := time.Now()
	cutoffTime := now.Add(-sa.windowDuration)

	// 使用二分搜索找到窗口内第一个有效数据点的索引
	startIndex := sort.Search(len(dataPoints), func(i int) bool {
		return dataPoints[i].Timestamp.After(cutoffTime)
	})

	// 如果没有找到有效数据点，返回0
	if startIndex >= len(dataPoints) {
		return 0
	}

	windowPoints := dataPoints[startIndex:]
	if len(windowPoints) < sa.minDataPoints {
		return 0
	}

	// 计算窗口内的总值
	totalValue := 0.0
	for _, point := range windowPoints {
		totalValue += point.Value
	}

	// 使用窗口的实际时间跨度
	timeSpan := windowPoints[len(windowPoints)-1].Timestamp.Sub(windowPoints[0].Timestamp)
	if timeSpan == 0 {
		// 当时间跨度为0时，使用最小时间间隔（1毫秒）来避免除零错误
		timeSpan = time.Millisecond
	}

	// 转换为指定单位
	denominator, ok := unitMap[unit]
	if !ok {
		denominator = unitMap[UnitS] // 默认使用秒
	}

	speedPerMs := totalValue / float64(timeSpan.Milliseconds())
	return speedPerMs * float64(denominator)
}

// cleanupOldData removes data points outside the window
func (sa *SpeedAggregate) cleanupOldData() {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	now := time.Now()
	cutoffTime := now.Add(-sa.windowDuration)

	for name, dataPoints := range sa.dataPoints {
		// 使用二分搜索找到第一个在窗口内的数据点
		validIndex := sort.Search(len(dataPoints), func(i int) bool {
			return dataPoints[i].Timestamp.After(cutoffTime)
		})

		// 计算被清理的数据点的总值，从totalCounters中减去
		if validIndex > 0 {
			removedValue := 0.0
			for i := 0; i < validIndex; i++ {
				removedValue += dataPoints[i].Value
			}
			sa.totalCounters[name] -= removedValue
		}

		// 保留窗口内的数据点
		if validIndex >= len(dataPoints) {
			// 所有数据点都过期了，清空
			sa.dataPoints[name] = []DataPoint{}
			sa.totalCounters[name] = 0
		} else if validIndex > 0 {
			// 有部分数据点过期，保留有效的
			sa.dataPoints[name] = dataPoints[validIndex:]
		}
		// validIndex == 0 时，所有数据点都在窗口内，不需要清理
	}

	sa.lastCleanup = now
}

// startCleanupRoutine starts a background goroutine for periodic cleanup
func (sa *SpeedAggregate) startCleanupRoutine() {
	sa.cleanupMu.Lock()
	defer sa.cleanupMu.Unlock()

	if sa.cleanupRunning {
		return
	}

	sa.cleanupRunning = true
	sa.cleanupWg.Add(1)

	go func() {
		defer sa.cleanupWg.Done()
		ticker := time.NewTicker(sa.cleanupInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				sa.cleanupOldData()
			case <-sa.stopCleanup:
				return
			}
		}
	}()
}

// Stop stops the background cleanup routine
func (sa *SpeedAggregate) Stop() {
	sa.cleanupMu.Lock()
	if sa.cleanupRunning {
		close(sa.stopCleanup)
		sa.cleanupRunning = false
		sa.cleanupMu.Unlock()
		// 等待清理goroutine完全停止
		sa.cleanupWg.Wait()
	} else {
		sa.cleanupMu.Unlock()
	}
}

// ToString returns a formatted string representation of the speed aggregate
func (sa *SpeedAggregate) ToString(unit Unit, toBeEstimated map[string]float64) string {
	// 在锁内快速复制所有需要的数据，减少锁持有时间
	sa.mu.RLock()
	dataPointsCopy := make(map[string][]DataPoint)
	countersCopy := make(map[string]float64)

	for name, dataPoints := range sa.dataPoints {
		if len(dataPoints) >= sa.minDataPoints {
			// 创建数据点的深拷贝，避免并发访问问题
			copied := make([]DataPoint, len(dataPoints))
			copy(copied, dataPoints)
			dataPointsCopy[name] = copied
		}
		countersCopy[name] = sa.totalCounters[name]
	}
	sa.mu.RUnlock()

	// 在锁外进行所有计算、排序和格式化操作
	speeds := make(map[string]float64)
	for name, dataPoints := range dataPointsCopy {
		speeds[name] = sa.calculateSpeedUnsafe(dataPoints, unit)
	}

	// 为没有足够数据点的指标设置速度为0
	for name := range countersCopy {
		if _, exists := speeds[name]; !exists {
			speeds[name] = 0
		}
	}

	// 按名称排序
	var names []string
	for name := range countersCopy {
		names = append(names, name)
	}
	sort.Strings(names)

	var result []string
	for _, name := range names {
		speed := speeds[name]
		counter := countersCopy[name]
		speedStr := numberToShow(speed)
		counterStr := numberToShow(counter)

		toShow := fmt.Sprintf("%s(%s):%s/%s[sliding]", name, counterStr, speedStr, unit)

		if toBeEstimated != nil {
			if target, ok := toBeEstimated[name]; ok && speed > 0 {
				remaining := target - counter
				if remaining > 0 {
					estimate := remaining / speed
					toShow += fmt.Sprintf(" est:%s %s", numberToShow(estimate), unit)
				}
			}
		}
		result = append(result, toShow)
	}

	return fmt.Sprintf("%v", result)
}

// GetTotalCounters returns the total accumulated values for all metrics
func (sa *SpeedAggregate) GetTotalCounters() map[string]float64 {
	sa.mu.RLock()
	defer sa.mu.RUnlock()

	counters := make(map[string]float64)
	for name, total := range sa.totalCounters {
		counters[name] = total
	}

	return counters
}

// GetDataPointCount returns the number of data points for each metric
func (sa *SpeedAggregate) GetDataPointCount() map[string]int {
	sa.mu.RLock()
	defer sa.mu.RUnlock()

	counts := make(map[string]int)
	for name, dataPoints := range sa.dataPoints {
		counts[name] = len(dataPoints)
	}

	return counts
}

// Reset clears all data points
func (sa *SpeedAggregate) Reset() {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	sa.dataPoints = make(map[string][]DataPoint)
	sa.totalCounters = make(map[string]float64)
	sa.counter = 0
	sa.lastCleanup = time.Now()
}
